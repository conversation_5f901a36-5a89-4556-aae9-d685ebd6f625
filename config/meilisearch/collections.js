const { includeContentType,
        cleanDuplicates,
        cleanEntryProperties,
        cleanCategories,
        fetchTeachersRelatedContent,
        handleSEOmapping,
        isoDateToTimestamp,
        mapRelatedContent,
        mapTeachers,
        mapSessionResourcesComponent,
} = require('./helpers')

module.exports = {
  "subscription-plan": {
    indexName: "plans",
    settings: {
      searchableAttributes: ['planId', 'promoId', 'name'],
      filterableAttributes: ['planId', 'planPrice', 'promoId', 'promoPrice'],
    },
    entriesQuery: {
      populate: [
        'picture',
        'featureList',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        featureList: entry.featureList.map(entry => ({
          feature: entry.feature,
          description: entry.description,
        })),
        id: entry.slug,
      }
    }
  },
  "category-groups": {
    indexName: "category-groups",
    settings: {
      searchableAttributes: ['title'],
      filterableAttributes: ['title', 'slug'],
    },
    entriesQuery: {
      populate: [
        'slug',
        'title',
        'color',
        'categories',
        'hubspot_list_id',
      ],
    },
    async transformEntry({ entry }) {
      const categories = entry.categories.map(category => category.slug)
      return {
        slug: entry.slug,
        title: entry.title,
        color: entry.colour,
        categories,
        hubspot_list_id: entry.hubspot_list_id,
        id: entry.slug,
      }
    },
  },
  category: {
    indexName: "categories",
    settings: {
      searchableAttributes: ['title'],
      filterableAttributes: ['title', 'slug'],
    },
    async transformEntry({ entry }) {
      return {
        title: entry.title,
        hubspot_list_id: entry.hubspot_list_id,
        slug: entry.slug,
        id: entry.slug,
      }
    },
  },
  entity: {
    indexName: "entities",
    settings: {
      searchableAttributes: ['name', 'description', 'bio'],
      filterableAttributes: ['name', 'description', 'bio'],
      sortableAttributes: ['ranking', 'name'],
    },
    entriesQuery: {
      populate: [
        'Related.course.picture',
        'Related.event.picture',
        'Related.article.picture',
        'Related.film.picture',
        'Related.podcast.picture',
        'picture',
        'cover',
        'relatedCourses.thumbnail',
        'relatedCourses.picture',
        'relatedEvents.thumbnail',
        'relatedEvents.picture',
        'relatedArticles.thumbnail',
        'relatedArticles.picture',
        'relatedFilms.thumbnail',
        'relatedFilms.picture',
        'relatedPodcasts.thumbnail',
        'relatedPodcasts.picture',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      let mappedEntry = cleanEntryProperties(entry)
      mappedEntry.thumbnail = entry.picture || null
      mappedEntry.picture = entry.cover || null

      const featured = includeContentType(entry.Related)

      const related = mapRelatedContent({
        courses: entry.relatedCourses,
        events: entry.relatedEvents,
        articles: entry.relatedArticles,
        films: entry.relatedFilms,
        podcasts: entry.relatedPodcasts,
      })

      delete mappedEntry.cover
      delete mappedEntry.relatedCourses
      delete mappedEntry.relatedEvents
      delete mappedEntry.relatedArticles
      delete mappedEntry.relatedFilms
      delete mappedEntry.relatedPodcasts

      return {
        ...mappedEntry,
        featured,
        related,
        cardTitle: entry.cardTitle || entry.name,
        ranking: entry.ranking || 0,
        seo: handleSEOmapping({ ...entry, title: entry.name }),
        id: mappedEntry.slug,
      }
    },
  },
  "course-session": {
    indexName: "lessons",
    settings: {
      searchableAttributes: ['title', 'description'],
      sortableAttributes: ['startDateTime'],
      filterableAttributes: [
        'id',
        'slug',
        'startDateTime',
        'startDateTimeTimestamp',
        'endDateTimeTimestamp',
        'teachers.slug',
        'teachers.name',
        'teachersList.teachers.slug',
        'teachersList.teachers.name',
        'q-and-a',
        'contentType',
      ],
    },
    entriesQuery: {
      populate: [
        'videoURL',
        'videoThumbnail',
        'videoTimecode',
        'audioURL',
        'transcript.attachment',
        'picture',
        'thumbnail',
        'readingResources.attachment',
        'practices.attachment',
        'teachersList.teachers.picture',
        'teachersList.teachers.cover',
        'teachersList.teachers.relatedCourses',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      const sessionEntry = entry
      const teachers = mapTeachers(entry.teachersList)
      delete entry.teachersList
      const readingResources = mapSessionResourcesComponent(entry.readingResources)
      const practices = mapSessionResourcesComponent(
          entry.practices)
      sessionEntry.duration = entry.videoURL?.duration || entry.videoURL?.asset_data?.duration || null

      return {
        ...sessionEntry,
        readingResources,
        practices,
        teachers,
        contentType: sessionEntry['q-and-a']? 'q-and-a' : 'chapters',
        startDateTimeTimestamp: isoDateToTimestamp(entry.startDateTime),
        endDateTimeTimestamp: isoDateToTimestamp(entry.endDateTime),
        seo: handleSEOmapping(entry),
        id: sessionEntry.slug,
      }
    },
  },
  course: {
    indexName: "courses",
    settings: {
      searchableAttributes: ['title', 'headline', 'description', 'about'],
      sortableAttributes: ['startDateTime'],
      filterableAttributes: [
        'id',
        'categories.slug',
        'categories.title',
        'modules.teachers.slug',
        'modules.teachers.name',
        'teachers.slug',
        'teachers.name',
        'teachers.slug',
        'teachers.name',
        'price',
        'dateHeadline',
        'startDateTime',
        'startDateTimeTimestamp',
        'endDateTimeTimestamp',
        'endDateTime',
        'chargebee_entity_id',
        'slug',
      ],
    },
    entriesQuery: {
      populate: [
        'categories',
        'Curriculum.KeyTakeaways',
        'modules.thumbnail',
        'modules.sessions.teachersList.teachers.picture',
        'modules.sessions.teachersList.teachers.cover',
        'modules.sessions.teachersList.teachers.relatedCourses',
        'modules.sessions.picture',
        'modules.sessions.thumbnail',
        'modules.sessions.videoURL',
        'liveConversations.sessions.teachersList.teachers.picture',
        'liveConversations.sessions.teachersList.teachers.cover',
        'liveConversations.sessions.teachersList.teachers.relatedCourses',
        'liveConversations.sessions.picture',
        'liveConversations.sessions.thumbnail',
        'liveConversations.sessions.videoURL',
        'Facilitators.facilitators.picture',
        'InfoBox.infoBoxElement.icon',
        'studentTestimonialHeader',
        'StudentTestimonial.picture',
        'readingResources.attachment',
        'relatedHeader',
        'Related.course.picture',
        'Related.event.picture',
        'Related.article.picture',
        'Related.film.picture',
        'Related.podcast.picture',
        'previewVideo',
        'previewThumbnail',
        'previewTimecode',
        'picture',
        'thumbnail',
        'discussionId',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      const modules = entry.modules
      const liveConversations = entry.liveConversations

      let teacherList = []
      modules.map(module => {
        module.thumbnail = module.thumbnail || module.sessions[0]?.thumbnail
        module.description = (module.description.length > 310)?
          `${module.description.substring(0, 307)}...` : module.description
        module.teachers = []
        module.sessions?.map(session => {
          session.duration = session.videoURL?.duration || session.videoURL?.asset_data?.duration || null

          delete session.videoURL
          delete session.audioURL
          delete session.liveURL
          delete session.readingResources
          delete session.practices

          session.teachers = []
          // gather all speakers inside event
          session.teachersList?.teachers.map(teacher => {
            const teacherObj = {
              id: teacher.id,
              name: teacher.name,
              cardTitle: teacher.cardTitle || teacher.name,
              slug: teacher.slug,
              thumbnail: teacher.picture,
              picture: teacher.cover,
              description: teacher.description,
              coursesTotal: teacher.relatedCourses?.length,
            }
            session.contentType = session["q-and-a"]? 'q-and-a' : 'chapters'
            session.directLink = `courses/${entry.slug}/${session.slug}`
            session.teachers.push(teacherObj)
            teacherList.push(teacherObj)
            module.teachers.push(teacherObj)
          })
          session.teachersTitle = session.teachersList?.title || null
          delete session.teachersList
        })
        module.chapters = module.sessions
        delete module.sessions
        module.teachers = cleanDuplicates(module.teachers)
      })

      // clean up liveConversations
      if (liveConversations) {
        liveConversations.thumbnail = liveConversations.sessions[0]?.thumbnail
        liveConversations.teachers = []
        liveConversations.sessions?.map(session => {
          session.duration = session.videoURL?.duration || session.videoURL?.asset_data?.duration || null
          session.contentType = 'q-and-a'

          delete session.videoURL
          delete session.audioURL
          delete session.liveURL
          delete session.readingResources
          delete session.practices

          session.teachers = []
          // gather all speakers inside event
          session.teachersList?.teachers.map(teacher => {
            const teacherObj = {
              id: teacher.id,
              name: teacher.name,
              cardTitle: teacher.cardTitle || teacher.name,
              slug: teacher.slug,
              thumbnail: teacher.picture,
              picture: teacher.cover,
              description: teacher.description,
              coursesTotal: teacher.relatedCourses?.length,
            }
            session.teachers.push(teacherObj)
            teacherList.push(teacherObj)
            liveConversations.teachers.push(teacherObj)
          })
          session.teachersTitle = session.teachersList?.title || null
          delete session.teachersList
        })
        liveConversations.chapters = liveConversations.sessions
        delete liveConversations.sessions
        liveConversations.teachers = cleanDuplicates(liveConversations.teachers)
      }

      const mappedEntry = cleanEntryProperties(entry)
      let related = includeContentType(entry.Related)

      // clean up facilitators
      const facilitators = mappedEntry.facilitators.facilitators
      mappedEntry.facilitators.facilitators = []
      facilitators.map(facilitator => {
        mappedEntry.facilitators.facilitators.push({
          id: facilitator.id,
          name: facilitator.name,
          cardTitle: facilitator.cardTitle || facilitator.name,
          slug: facilitator.slug,
          thumbnail: facilitator.picture,
          picture: facilitator.cover,
          description: facilitator.description,
        })
      })

      const readingResources = mapSessionResourcesComponent(entry.readingResources)

      const relatedHeader = entry.relatedHeader?.title?
                            entry.relatedHeader : { title: 'related content',
                                                    description: '',
                                                    id: 1 }

      const studentTestimonialHeader = entry.studentTestimonialHeader?.title?
                            entry.studentTestimonialHeader : { title: 'testimonials',
                                                                description: 'see what over 7000+ users say about advaya',
                                                                id: 1 }

      return {
        ...mappedEntry,
        private: mappedEntry.private || false,
        modules,
        liveConversations,
        studentTestimonialHeader,
        readingResources,
        relatedHeader,
        related,
        teachers: cleanDuplicates(teacherList),
        categories: cleanCategories(mappedEntry.categories),
        previewThumbnail: entry.previewThumbnail || entry.thumbnail || entry.picture,
        startDateTimeTimestamp: isoDateToTimestamp(entry.startDateTime),
        endDateTimeTimestamp: isoDateToTimestamp(entry.endDateTime),
        seo: handleSEOmapping(entry),
        contentType: 'courses',
        id: mappedEntry.slug,
      }
    },
  },
  "event-session": {
    indexName: "sessions",
    settings: {
      searchableAttributes: ['title', 'description'],
      sortableAttributes: ['startDateTime'],
      filterableAttributes: [
        'id',
        'slug',
        'startDateTime',
        'teachers.slug',
        'teachers.name',
        'teachersList.teachers.slug',
        'teachersList.teachers.name',
        'categories.slug',
        'dateHeadline',
        'startDateTimeTimestamp',
        'endDateTimeTimestamp',
        'headline',
        'contentType',
      ],
    },
    entriesQuery: {
      populate: [
        'videoURL',
        'videoThumbnail',
        'videoTimecode',
        'audioURL',
        'transcript.attachment',
        'picture',
        'thubmnail',
        'readingResources.attachment',
        'practices.attachment',
        'teachersList.teachers.picture',
        'teachersList.teachers.cover',
        'teachersList.teachers.relatedCourses',
        'event.slug',
        'event.categories.slug',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      const sessionEntry = entry
      const teachers = mapTeachers(entry.teachersList)
      // TODO: delete entry.teachersList, update in fetchTeachersContent
      const readingResources = mapSessionResourcesComponent(entry.readingResources)
      const practices = mapSessionResourcesComponent(
          entry.practices)
      sessionEntry.duration = entry.videoURL?.duration || entry.videoURL?.asset_data?.duration || null
      const contentType = sessionEntry.event?.recurring? 'clubs' : 'audio-journeys'

      return {
        ...sessionEntry,
        readingResources,
        practices,
        teachers,
        categories: cleanCategories(sessionEntry.event?.categories),
        seo: handleSEOmapping(entry),
        id: sessionEntry.slug,
        seriesSlug: sessionEntry.event?.slug,
        seriesTitle: sessionEntry.event?.title,
        cardTitle: sessionEntry.cardTitle || sessionEntry.title,
        directLink: `${contentType}/${sessionEntry.event.slug}/${sessionEntry.slug}`,
        startDateTimeTimestamp: isoDateToTimestamp(entry.startDateTime),
        endDateTimeTimestamp: isoDateToTimestamp(entry.endDateTime),
        contentType,
      }
    },
  },
  event: {
    indexName: "events",
    settings: {
      searchableAttributes: ['title', 'headline', 'description', 'about'],
      sortableAttributes: ['startDateTime'],
      filterableAttributes: [
        'id',
        'categories.slug',
        'categories.title',
        'teachers.slug',
        'teachers.name',
        'modules.teachers.slug',
        'modules.teachers.name',
        'teachers.slug',
        'teachers.name',
        'price',
        'dateHeadline',
        'startDateTime',
        'startDateTimeTimestamp',
        'endDateTimeTimestamp',
        'endDateTime',
        'chargebee_entity_id',
        'recurring',
        'slug',
      ],
    },
    entriesQuery: {
      populate: [
        'categories',
        'Curriculum.KeyTakeaways',
        'modules.sessions.teachersList.teachers.picture',
        'modules.sessions.teachersList.teachers.cover',
        'modules.sessions.teachersList.teachers.relatedCourses',
        'modules.sessions.picture',
        'modules.sessions.thumbnail',
        'modules.sessions.videoURL',
        'Facilitators.facilitators.picture',
        'InfoBox.infoBoxElement.icon',
        'StudentTestimonial.picture',
        'Related.course.picture',
        'Related.event.picture',
        'Related.article.picture',
        'Related.film.picture',
        'Related.podcast.picture',
        'previewVideo',
        'previewThumbnail',
        'previewTimecode',
        'picture',
        'thumbnail',
        'discussionId',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      const modules = entry.modules
      let teacherList = []
      const contentType = (entry.recurring || false)? 'clubs' : 'audio-journeys'
      modules.map(module => {
        module.thumbnail = module.sessions[0]?.thumbnail
        module.teachers = []
        module.sessions?.map(session => {
          session.duration = session.videoURL?.duration || session.videoURL?.asset_data?.duration || null

          delete session.videoURL
          delete session.audioURL
          delete session.liveURL
          delete session.readingResources
          delete session.practices

          session.teachers = []
          // gather all speakers inside event
          session.teachersList?.teachers.map(teacher => {
            const teacherObj = {
              id: teacher.id,
              name: teacher.name,
              cardTitle: teacher.cardTitle || teacher.name,
              slug: teacher.slug,
              thumbnail: teacher.picture,
              picture: teacher.cover,
              description: teacher.description,
              coursesTotal: teacher.relatedCourses?.length,
            }
            session.contentType = contentType
            session.directLink = `${contentType}/${entry.slug}/${session.slug}`
            session.teachers.push(teacherObj)
            teacherList.push(teacherObj)
            module.teachers.push(teacherObj)
          })
          session.teachersTitle = session.teachersList?.title || null
          delete session.teachersList
        })
        module.chapters = module.sessions
        delete module.sessions
        module.teachers = cleanDuplicates(module.teachers)
      })

      const mappedEntry = cleanEntryProperties(entry)
      mappedEntry.recurring = mappedEntry.recurring || false
      let related = includeContentType(entry.Related)

      // clean up facilitators
      const facilitators = mappedEntry.facilitators.facilitators
      mappedEntry.facilitators.facilitators = []
      facilitators.map(facilitator => {
        mappedEntry.facilitators.facilitators.push({
          id: facilitator.id,
          name: facilitator.name,
          cardTitle: facilitator.cardTitle || facilitator.name,
          slug: facilitator.slug,
          thumbnail: facilitator.picture,
          picture: facilitator.cover,
          description: facilitator.description,
        })
      })

      return {
        ...mappedEntry,
        modules,
        related,
        teachers: cleanDuplicates(teacherList),
        categories: cleanCategories(mappedEntry.categories),
        previewThumbnail: entry.previewThumbnail || entry.thumbnail || entry.picture,
        seo: handleSEOmapping(entry),
        contentType: mappedEntry.recurring? 'clubs' : 'audio-journeys',
        startDateTimeTimestamp: isoDateToTimestamp(entry.startDateTime),
        endDateTimeTimestamp: isoDateToTimestamp(entry.endDateTime),
        id: mappedEntry.slug,
      }
    },
  },
  "watch-entry": {
    indexName: "films",
    settings: {
      searchableAttributes: ['title', 'description', 'about'],
      sortableAttributes: ['date'],
      filterableAttributes: [
        'categories.slug',
        'categories.title',
        'teachers.slug',
        'teachers.name',
        'TeacherList.teachers.slug',
        'TeacherList.teachers.name',
        'price',
        'chargebee_entity_id',
        'slug',
      ],
    },
    entriesQuery: {
      populate: [
        'picture',
        'thumbnail',
        'categories',
        'TeacherList.teachers.picture',
        'TeacherList.teachers.cover',
        'TeacherList.teachers.relatedCourses',
        'Related.course.picture',
        'Related.event.picture',
        'Related.article.picture',
        'Related.film.picture',
        'Related.podcast.picture',
        'readingResources.attachment',
        'videoURL',
        'videoThumbnail',
        'videoTimecode',
        'youtubeVideoID',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      let mappedEntry = entry
      let related = includeContentType(entry.Related)
      mappedEntry.teachers = entry.TeacherList?.teachers.map(teacher => {
        return {
          id: teacher.id,
          name: teacher.name,
          cardTitle: teacher.cardTitle || teacher.name,
          slug: teacher.slug,
          thumbnail: teacher.picture,
          picture: teacher.cover,
          description: teacher.description,
          coursesTotal: teacher.relatedCourses?.length,
        }
      })
      const readingResources = mapSessionResourcesComponent(entry.readingResources)
      delete mappedEntry.Related
      delete mappedEntry.TeacherList

      return {
        ...mappedEntry,
        related,
        readingResources,
        categories: cleanCategories(mappedEntry.categories),
        seo: handleSEOmapping(entry),
        contentType: 'films',
        id: mappedEntry.slug,
      }
    },
  },
  "read-entry": {
    indexName: "articles",
    settings: {
      searchableAttributes: ['title', 'description', 'content'],
      sortableAttributes: ['date'],
      filterableAttributes: [
        'categories.slug',
        'categories.title',
        'teachers.slug',
        'teachers.name',
        'TeacherList.teachers.slug',
        'TeacherList.teachers.name',
        'price',
        'chargebee_entity_id',
      ],
    },
    entriesQuery: {
      populate: [
        'picture',
        'thumbnail',
        'categories',
        'TeacherList.teachers.picture',
        'TeacherList.teachers.cover',
        'TeacherList.teachers.relatedCourses',
        'Related.course.picture',
        'Related.event.picture',
        'Related.article.picture',
        'Related.film.picture',
        'Related.podcast.picture',
        'readingResources.attachment',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      let mappedEntry = entry
      let related = includeContentType(entry.Related)
      mappedEntry.teachers = entry.TeacherList?.teachers.map(teacher => {
        return {
          id: teacher.id,
          name: teacher.name,
          cardTitle: teacher.cardTitle || teacher.name,
          slug: teacher.slug,
          thumbnail: teacher.picture,
          picture: teacher.cover,
          description: teacher.description,
          coursesTotal: teacher.relatedCourses?.length,
        }
      })
      const readingResources = mapSessionResourcesComponent(entry.readingResources)
      delete mappedEntry.Related
      delete mappedEntry.TeacherList

      return {
        ...mappedEntry,
        related,
        readingResources,
        categories: cleanCategories(mappedEntry.categories),
        seo: handleSEOmapping(entry),
        contentType: 'articles',
        id: mappedEntry.slug,
      }
    },
  },
  "listen-entry": {
    indexName: "podcasts",
    settings: {
      searchableAttributes: ['title', 'description', 'about'],
      sortableAttributes: ['date'],
      filterableAttributes: [
        'categories.slug',
        'categories.title',
        'teachers.slug',
        'teachers.name',
        'TeacherList.teachers.slug',
        'TeacherList.teachers.name',
        'price',
        'chargebee_entity_id',
        'slug',
      ],
    },
    entriesQuery: {
      populate: [
        'picture',
        'thumbnail',
        'categories',
        'TeacherList.teachers.picture',
        'TeacherList.teachers.cover',
        'TeacherList.teachers.relatedCourses',
        'Related.course.picture',
        'Related.event.picture',
        'Related.article.picture',
        'Related.film.picture',
        'Related.podcast.picture',
        'audioURL',
        'readingResources.attachment',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      let mappedEntry = entry
      let related = includeContentType(entry.Related)
      mappedEntry.teachers = entry.TeacherList?.teachers.map(teacher => {
        return {
          id: teacher.id,
          name: teacher.name,
          cardTitle: teacher.cardTitle || teacher.name,
          slug: teacher.slug,
          thumbnail: teacher.picture,
          picture: teacher.cover,
          description: teacher.description,
          coursesTotal: teacher.relatedCourses?.length,
        }
      })
      delete mappedEntry.Related
      delete mappedEntry.TeacherList
      const readingResources = mapSessionResourcesComponent(entry.readingResources)

      return {
        ...mappedEntry,
        related,
        readingResources,
        categories: cleanCategories(mappedEntry.categories),
        seo: handleSEOmapping(entry),
        contentType: 'podcasts',
        id: mappedEntry.slug,
      }
    },
  },
  "webinar": {
    indexName: "webinars",
    settings: {
      searchableAttributes: ['title', 'description', 'about'],
      sortableAttributes: ['startDateTime'],
      filterableAttributes: [
        'categories.slug',
        'categories.title',
        'teachers.slug',
        'teachers.name',
        'TeacherList.teachers.slug',
        'TeacherList.teachers.name',
        'startDateTime',
        'endDateTime',
        'startDateTimeTimestamp',
        'endDateTimeTimestamp',
        'price',
        'chargebee_entity_id',
        'slug',
      ],
    },
    entriesQuery: {
      populate: [
        'videoURL',
        'picture',
        'thumbnail',
        'categories',
        'Curriculum.KeyTakeaways',
        'TeacherList.teachers.picture',
        'TeacherList.teachers.cover',
        'TeacherList.teachers.relatedCourses',
        'Related.course.picture',
        'Related.event.picture',
        'Related.article.picture',
        'Related.film.picture',
        'Related.podcast.picture',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      let mappedEntry = cleanEntryProperties(entry)
      let related = includeContentType(entry.Related)
      let seo = entry.seo? handleSEOmapping(entry) : null
      mappedEntry.teachers = entry.TeacherList?.teachers.map(teacher => {
        return {
          id: teacher.id,
          name: teacher.name,
          cardTitle: teacher.cardTitle || teacher.name,
          slug: teacher.slug,
          thumbnail: teacher.picture,
          picture: teacher.cover,
          description: teacher.description,
          coursesTotal: teacher.relatedCourses?.length,
        }
      })
      delete mappedEntry.Related
      delete mappedEntry.TeacherList

      delete mappedEntry.livestreamId
      delete mappedEntry.streamKey

      return {
        ...mappedEntry,
        related,
        seo,
        categories: cleanCategories(mappedEntry.categories),
        startDateTimeTimestamp: isoDateToTimestamp(entry.startDateTime),
        endDateTimeTimestamp: isoDateToTimestamp(entry.endDateTime),
        contentType: 'webinars',
        id: mappedEntry.slug,
      }
    },
  },
}
