const {
  aggregateModuleTeachers,
  cleanDuplicates,
  cleanCategories,
  cleanMagazineFeatured,
  mapEventSessions,
  mapHeader,
  mapTeachers,
} = require('./helpers');


module.exports = {
  homepage: {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'header.thumbnail',
        'header.previewVideo',
        'intro.description',
        'intro.header.title',
        'intro.header.description',
        'intro.videoList.video',
        'intro.videoList.thumbnail',
        'learnHeader',
        'featuredWebinarsHeader',
        'featuredWebinarsList.webinar.thumbnail',
        'featuredWebinarsList.webinar.picture',
        'featuredWebinarsList.webinar.thumbnail',
        'featuredWebinarsList.webinar.TeacherList.teachers.picture',
        'featuredWebinarsList.webinar.TeacherList.teachers.cover',
        'featuredWebinarsList.webinar.TeacherList.teachers.relatedCourses',
        'upcomingCoursesHeader',
        'upcomingCoursesList.course.thumbnail',
        'upcomingCoursesList.course.picture',
        'upcomingCoursesList.course.modules.sessions.teachersList.teachers.cover',
        'upcomingCoursesList.course.modules.sessions.teachersList.teachers.picture',
        'upcomingCoursesList.course.modules.sessions.teachersList.teachers.relatedCourses',
        'featuredCoursesHeader',
        'featuredCoursesList.course.thumbnail',
        'featuredCoursesList.course.picture',
        'featuredCoursesList.course.modules.sessions.teachersList.teachers.cover',
        'featuredCoursesList.course.modules.sessions.teachersList.teachers.picture',
        'featuredCoursesList.course.modules.sessions.teachersList.teachers.relatedCourses',
        'practiceHeader',
        'featuredPracticesHeader',
        'featuredPracticesList.eventSession.thumbnail',
        'featuredPracticesList.eventSession.picture',
        'featuredPracticesList.eventSession.teachersList.teachers.cover',
        'featuredPracticesList.eventSession.teachersList.teachers.picture',
        'featuredPracticesList.eventSession.teachersList.teachers.relatedCourses',
        'featuredPracticesList.eventSession.event.thumbnail',
        'featuredPracticesList.eventSession.event.picture',
        'featuredPracticesList.eventSession.event.teachers.cover',
        'featuredPracticesList.eventSession.event.teachers.picture',
        'featuredPracticesList.eventSession.event.teachers.relatedCourses',
        'featuredClubsHeader',
        'featuredClubsList.eventSession.thumbnail',
        'featuredClubsList.eventSession.picture',
        'featuredClubsList.eventSession.teachersList.teachers.cover',
        'featuredClubsList.eventSession.teachersList.teachers.picture',
        'featuredClubsList.eventSession.teachersList.teachers.relatedCourses',
        'featuredClubsList.eventSession.event.thumbnail',
        'featuredClubsList.eventSession.event.picture',
        'featuredClubsList.eventSession.event.teachers.cover',
        'featuredClubsList.eventSession.event.teachers.picture',
        'featuredClubsList.eventSession.event.teachers.relatedCourses',
        'teachersHeader',
        'teachersList.teachers.cover',
        'teachersList.teachers.picture',
        'teachersList.teachers.relatedCourses',
        'membershipHeader',
        'membershipList.entry.thumbnail',
        'membershipList.entry.video',
        'testimonialsHeader',
        'testimonialsList.picture',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      let featuredWebinarsList = [],
          featuredCoursesList = [],
          featuredPracticesList = [],
          featuredClubsList = [],
          membershipList = []

      // flatten webinars list
      for (let w of entry.featuredWebinarsList) {
        const teachers = mapTeachers(w.webinar?.TeacherList)
        delete w.webinar?.TeacherList
        delete w.webinar?.about
        delete w.webinar?.createdAt
        delete w.webinar?.updatedAt
        delete w.webinar?.publishedAt

        featuredWebinarsList.push({
          contentType: "webinars",
          directLink: `webinars/${w.webinar.slug}`,
          ...w.webinar,
          teachers })
      }

      // flatten featured courses
      for (let c of entry.featuredCoursesList) {
        const teachers = aggregateModuleTeachers(c.course?.modules)
        delete c.course?.modules
        delete c.course?.about
        delete c.course?.createdAt
        delete c.course?.updatedAt
        delete c.course?.publishedAt

        featuredCoursesList.push({
          contentType: "courses",
          directLink: `courses/${c.course.slug}`,
          ...c.course,
          teachers })
      }

      // flatten upcoming audio journey chapters
      for (let e of entry.featuredPracticesList) {
        if (e.eventSession?.event) {
          const teachers = mapTeachers(e.eventSession?.teachersList)
          featuredPracticesList.push({ ...mapEventSessions(e, 'audio-journeys'), teachers })
        }
      }

      // flatten upcoming club chapters
      for (let e of entry.featuredClubsList) {
        if (e.eventSession?.event) {
          const teachers = mapTeachers(e.eventSession?.teachersList)
          featuredClubsList.push({ ...mapEventSessions(e, 'clubs'), teachers })
        }
      }

      // flatten teachers list
      const teachersList = mapTeachers(entry.teachersList)

      // flatten membership entries list
      for (let e of entry.membershipList.entry) membershipList.push(e)

      // remap header links
      const headersList = [
        'featuredWebinarsHeader',
        'featuredCoursesHeader',
        'featuredPracticesHeader',
        'featuredClubsHeader']
      for (let header of headersList)
        if (entry[header]) entry[header] = mapHeader(entry[header])

      const categories =  await strapi.entityService.findMany('api::category.category')

      const displayInPage = true
      const plans = await strapi.service('api::subscription-plan.subscription-plan').getPlans(displayInPage)

      return {
        ...entry,
        slug: 'homepage',
        id: 'homepage',
        featuredWebinarsList,
        featuredCoursesList,
        featuredPracticesList,
        featuredClubsList,
        membershipList,
        teachersList,
        plans,
        categories: cleanCategories(categories),
      }
    },
  },
  "about-us": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'header.picture',
        'content.about',
        'ourThoughtsHeader',
        'ourThoughtsList.articles.picture',
        'ourThoughtsList.articles.thumbnail',
        'features',
        'ourProgrammesHeader',
        'ourProgrammesList.ProgrammeListElement.picture',
        'team.TeamMemberElements.picture',
        'topicsHeader',
        'topicsList.ProgrammeListElement.picture',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'about-us',
        id: 'about-us',
        ourThoughtsList: {
          contentType: 'articles', items: entry.ourThoughtsList.articles,
        },
        ourProgrammesList: entry.ourProgrammesList?.ProgrammeListElement || null,
        topicsList: entry.topicsList?.ProgrammeListElement || null,
      }
    },
  },
  partners: {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'title',
        'description',
        'content',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'partners',
        id: 'partners',
      }
    },
  },
  teachers: {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'header',
        'featuredTeachersHeader',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        featuredTeachersHeader: mapHeader(entry.featuredTeachersHeader),
        slug: 'teachers',
        id: 'teachers',
      }
    },
  },
  "faq-and-contact-us": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'header.picture',
        'faq.FAQ',
        'contact',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'faq-and-contact-us',
        id: 'faq-and-contact-us',
      }
    },
  },
  // "courses-landing": {
  //   indexName: 'pages',
  //   entriesQuery: {
  //     populate: [
  //       'header',
  //       'presentation.picture',
  //       'teachers.picture',
  //       'teaser.picture',
  //       'mediaCTA.IconCTAElementList',
  //       'featuredList.course.picture',
  //       'featuredList.course.modules.sessions.teachersList.teachers.picture',
  //       'seo.metaImage',
  //       'seo.metaSocial.image',
  //     ],
  //   },
  //   async transformEntry({ entry }) {
  //     let featuredList = []
  //
  //     // flatten courses list
  //     for (let c of entry.featuredList) {
  //       let teacherList = []
  //       c.course?.modules?.map(module => {
  //         module.sessions?.map(session => {
  //           // gather all speakers inside course
  //           session.teachersList?.teachers.map(teacher => {
  //             const teacherObj = {
  //               id: teacher.id,
  //               name: teacher.name,
  //               // description: teacher.description,
  //               // bio: teacher.bio,
  //               slug: teacher.slug,
  //               thumbnail: teacher.picture,
  //               picture: teacher.cover,
  //               coursesTotal: teacher.relatedCourses?.length,
  //             }
  //             teacherList.push(teacherObj)
  //           })
  //         })
  //       })
  //       delete c.course?.modules
  //       delete c.course?.about
  //
  //       featuredList.push({
  //         contentType: "courses",
  //         ...c.course,
  //         teachers: cleanDuplicates(teacherList),
  //       })
  //     }
  //
  //     const cta = entry.mediaCTA
  //     delete entry.mediaCTA
  //
  //     return {
  //       ...entry,
  //       slug: 'courses-landing',
  //       id: 'courses-landing',
  //       featuredList,
  //       cta,
  //       presentation: entry.presentation || null,
  //       teachers: entry.teachers || null,
  //       teaser: entry.teaser || null,
  //     }
  //   },
  // },
  // "events-landing": {
  //   indexName: 'pages',
  //   entriesQuery: {
  //     populate: [
  //       'header',
  //       'presentation.picture',
  //       'teachers.TeacherList.teachers.picture',
  //       'teaser.picture',
  //       'mediaCTA.IconCTAElementList',
  //       'featuredList.event.picture',
  //       'featuredList.event.modules.sessions.teachersList.teachers.picture',
  //       // 'featuredList.eventSession.thumbnail',
  //       // 'featuredList.eventSession.picture',
  //       // 'featuredList.eventSession.teachersList.teachers.picture',
  //       // 'featuredList.eventSession.event.picture',
  //       // 'featuredList.eventSession.event.teachers.picture',
  //       // 'featuredList.eventSession.event.modules.sessions.teachersList.teachers.picture',
  //       'seo.metaImage',
  //       'seo.metaSocial.image',
  //     ],
  //   },
  //   async transformEntry({ entry }) {
  //     let featuredList = []
  //
  //     // flatten events list
  //     for (let c of entry.featuredList) {
  //       let teacherList = []
  //       c.event?.modules?.map(module => {
  //         module.sessions?.map(session => {
  //           // gather all speakers inside event
  //           session.teachersList?.teachers.map(teacher => {
  //             const teacherObj = {
  //               id: teacher.id,
  //               name: teacher.name,
  //               // description: teacher.description,
  //               // bio: teacher.bio,
  //               slug: teacher.slug,
  //               thumbnail: teacher.picture,
  //               picture: teacher.cover,
  //               coursesTotal: teacher.relatedCourses?.length,
  //             }
  //             teacherList.push(teacherObj)
  //           })
  //         })
  //       })
  //       delete c.event?.modules
  //       delete c.event?.about
  //
  //       featuredList.push({
  //         contentType: "events",
  //         ...c.event,
  //         teachers: cleanDuplicates(teacherList),
  //       })
  //     }
  //
  //     // flatten upcoming event sessionss
  //     // for (let e of entry.featuredList) {
  //     //   if (e.eventSession.event) {
  //     //     featuredList.push({ contentType: "event-session", ...mapEventSessions(e) })
  //     //   }
  //     // }
  //
  //     const cta = entry.mediaCTA
  //     delete entry.mediaCTA
  //
  //     return {
  //       ...entry,
  //       slug: 'events-landing',
  //       id: 'events-landing',
  //       featuredList,
  //       cta,
  //       presentation: entry.presentation || null,
  //       teachers: entry.teachers || null,
  //       teaser: entry.teaser || null,
  //     }
  //   },
  // },
  "magazine-landing": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'magazineArticle.picture',
        'magazineArticle.video',
        'magazineArticle.featuredArticle.article.picture',
        'magazineArticle.featuredArticle.article.TeacherList.teachers.picture',
        'magazineFilm.picture',
        'magazineFilm.video',
        'magazineFilm.featuredFilm.film.picture',
        'magazineFilm.featuredFilm.film.TeacherList.teachers.picture',
        'magazinePodcast.picture',
        'magazinePodcast.video',
        'magazinePodcast.featuredPodcast.podcast.picture',
        'magazinePodcast.featuredPodcast.podcast.TeacherList.teachers.picture',
        'ctaText',
        'linkText',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      const featuredArticle = {
        ...cleanMagazineFeatured(entry.magazineArticle.featuredArticle.article, true),
        video: entry.magazineArticle.video,
        picture: entry.magazineArticle.picture,
        contentType: 'articles',
      }

      const featuredFilm = {
        ...cleanMagazineFeatured(entry.magazineFilm.featuredFilm.film),
        video: entry.magazineFilm.video,
        picture: entry.magazineFilm.picture,
        contentType: 'films',
      }

      const featuredPodcast = {
        ...cleanMagazineFeatured(entry.magazinePodcast.featuredPodcast.podcast),
        video: entry.magazinePodcast.video,
        picture: entry.magazinePodcast.picture,
        contentType: 'podcasts',
      }

      return {
        featuredArticle,
        featuredFilm,
        featuredPodcast,
        ctaText: entry.ctaText,
        linkText: entry.linkText,
        slug: 'magazine-landing',
        id: 'magazine-landing',
        seo: entry.seo,
      }
    },
  },
  // "watch-landing": {
  //   indexName: 'pages',
  //   entriesQuery: {
  //     populate: [
  //       'header',
  //       'promo.picture',
  //       'promo.category',
  //       'search',
  //       'cta.IconCTAElementList.icon',
  //       'featuredList.film.picture',
  //       'featuredList.film.TeacherList.teachers.picture',
  //       'seo.metaImage',
  //       'seo.metaSocial.image',
  //     ],
  //   },
  //   async transformEntry({ entry }) {
  //     let featuredList = []
  //
  //     // flatten film list
  //     for (let c of entry.featuredList) {
  //       const teachers = c.film?.TeacherList?.teachers
  //       delete c.film?.TeacherList
  //
  //       featuredList.push({ contentType: "films", ...c.film, teachers })
  //     }
  //
  //     return {
  //       ...entry,
  //       slug: 'watch-landing',
  //       id: 'watch-landing',
  //       featuredList,
  //     }
  //   },
  // },
  // "read-landing": {
  //   indexName: 'pages',
  //   entriesQuery: {
  //     populate: [
  //       'header',
  //       'promo.picture',
  //       'promo.category',
  //       'search',
  //       'cta.IconCTAElementList.icon',
  //       'featuredList.article.picture',
  //       'featuredList.article.TeacherList.teachers.picture',
  //       'seo.metaImage',
  //       'seo.metaSocial.image',
  //     ],
  //   },
  //   async transformEntry({ entry }) {
  //     let featuredList = []
  //
  //     // flatten articles list
  //     for (let c of entry.featuredList) {
  //       const teachers = c.article?.TeacherList?.teachers
  //       for (let teacher of teachers) {
  //         teacher = {
  //           slug: teacher.slug,
  //           name: teacher.name,
  //           thumbnail: teacher.picture,
  //           picture: teacher.cover,
  //           coursesTotal: teacher.relatedCourses?.length,
  //         }
  //       }
  //       delete c.article?.TeacherList
  //
  //       featuredList.push({ contentType: "articles", ...c.article, teachers })
  //     }
  //
  //     return {
  //       ...entry,
  //       slug: 'read-landing',
  //       id: 'read-landing',
  //       featuredList,
  //     }
  //   },
  // },
  // "listen-landing": {
  //   indexName: 'pages',
  //   entriesQuery: {
  //     populate: [
  //       'header',
  //       'promo.picture',
  //       'promo.category',
  //       'search',
  //       'cta.IconCTAElementList.icon',
  //       'featuredList.podcast.picture',
  //       'featuredList.podcast.TeacherList.teachers.picture',
  //       'featuredList.podcast.TeacherList.teachers.cover',
  //       'seo.metaImage',
  //       'seo.metaSocial.image',
  //     ],
  //   },
  //   async transformEntry({ entry }) {
  //     let featuredList = []
  //
  //     // flatten podcast list
  //     for (let c of entry.featuredList) {
  //       const teachers = c.podcast?.TeacherList?.teachers
  //       delete c.podcast?.TeacherList
  //
  //       featuredList.push({ contentType: "podcasts", ...c.podcast, teachers })
  //     }
  //
  //     return {
  //       ...entry,
  //       slug: 'listen-landing',
  //       id: 'listen-landing',
  //       featuredList,
  //     }
  //   },
  // },
  // "interests-landing": {
  //   indexName: 'pages',
  //   entriesQuery: {
  //     populate: [
  //       'teaser.course.picture',
  //       'mediaCTA.HorizontalIconCTAElement',
  //       'promo.picture',
  //       'promo.category',
  //       'categoriesCTA.IconCTAElementList.icon',
  //       'seo.metaImage',
  //       'seo.metaSocial.image',
  //     ],
  //   },
  //   async transformEntry({ entry }) {
  //     const cta = entry.mediaCTA
  //     delete entry.mediaCTA
  //
  //     return {
  //       ...entry,
  //       slug: 'interests-landing',
  //       id: 'interests-landing',
  //       cta,
  //     }
  //   },
  // },
  // "interests-by-speaker": {
  //   indexName: 'pages',
  //   entriesQuery: {
  //     populate: [
  //       'header.picture',
  //       'header.icon',
  //       'teaser.course.picture',
  //       'cta.IconCTAElementList.icon',
  //       'seo.metaImage',
  //       'seo.metaSocial.image',
  //     ],
  //   },
  //   async transformEntry({ entry }) {
  //     return {
  //       ...entry,
  //       slug: 'interests-by-speaker',
  //       id: 'interests-by-speaker',
  //     }
  //   },
  // },
  // "webinars-landing": {
  //   indexName: 'pages',
  //   entriesQuery: {
  //     populate: [
  //       'header',
  //       'presentation.picture',
  //       'teachers.picture',
  //       'teaser.picture',
  //       'mediaCTA.IconCTAElementList',
  //       'featuredList.webinar.picture',
  //       'featuredList.webinar.TeacherList.teachers.picture',
  //       'featuredList.webinar.TeacherList.teachers.cover',
  //       'seo.metaImage',
  //       'seo.metaSocial.image',
  //     ],
  //   },
  //   async transformEntry({ entry }) {
  //     let featuredList = []
  //
  //     // flatten webinar list
  //     for (let c of entry.featuredList) {
  //       const teachers = c.webinar?.TeacherList?.teachers
  //       delete c.webinar?.TeacherList
  //
  //       featuredList.push({ contentType: "webinars", ...c.webinar, teachers })
  //     }
  //
  //     const cta = entry.mediaCTA
  //     delete entry.mediaCTA
  //
  //     return {
  //       ...entry,
  //       slug: 'webinars-landing',
  //       id: 'webinars-landing',
  //       featuredList,
  //       cta,
  //       presentation: entry.presentation || null,
  //       teachers: entry.teachers || null,
  //       teaser: entry.teaser || null,
  //     }
  //   },
  // },
  footer: {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'stickyBarMessageFree',
        'stickyBarMessageCommunity',
        'stickyBarMessageThrive',
        'membershipOptionsTitle',
        'membershipOptionsDescription',
        'firstColumn.linksList',
        'secondColumn.linksList',
        'social.icon',
        'newsletter',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'footer',
        id: 'footer',
      }
    },
  },
  "privacy-policy": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'content',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'privacy-policy',
        id: 'privacy-policy',
      }
    },
  },
  "recording-policy": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'content',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'recording-policy',
        id: 'recording-policy',
      }
    },
  },
  "terms-and-conditions": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'content',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'terms-and-conditions',
        id: 'terms-and-conditions',
      }
    },
  },
  "cookie-consent": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'title',
        'description',
        'content',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'cookie-consent',
        id: 'cookie-consent',
      }
    },
  },
  "contact": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'title',
        'description',
        'contact',
        'content',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'contact',
        id: 'contact',
      }
    },
  },
  "philosophy": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'title',
        'description',
        'content',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'philosophy',
        id: 'philosophy',
      }
    },
  },
  "vacancies": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'title',
        'description',
        'content',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'vacancies',
        id: 'vacancies',
      }
    },
  },
  "the-method": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'title',
        'description',
        'content',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'the-method',
        id: 'the-method',
      }
    },
  },
  "affiliates": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'title',
        'description',
        'content',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'affiliates',
        id: 'affiliates',
      }
    },
  },
  "about": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'title',
        'description',
        'content',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      return {
        ...entry,
        slug: 'about',
        id: 'about',
      }
    },
  },
  // membership: {
  //   indexName: 'pages',
  //   entriesQuery: {
  //     populate: [
  //       'header',
  //       'membershipPlans.subscription_plan.picture',
  //       'memberBenefitsHeader',
  //       'memberBenefitsList',
  //       'membersTestimonialHeader',
  //       'membersTestimonialList.picture',
  //       'membersTestimonialList.tagList',
  //       'studentsTestimonialHeader',
  //       'studentsTestimonialList.picture',
  //       'studentsTestimonialList.tagList',
  //       'seo.metaImage',
  //       'seo.metaSocial.image',
  //     ],
  //   },
  //   async transformEntry({ entry }) {
  //     const displayInPage = true
  //     const plans = await strapi.service('api::subscription-plan.subscription-plan').getPlans(displayInPage)
  //     const membersTestimonialList = entry.membersTestimonialList.map(testimonialElement => {
  //       const tagList = testimonialElement.tagList.map(el => el.tag)
  //       return {
  //         ...testimonialElement,
  //         tagList,
  //       }
  //     })
  //     const studentsTestimonialList = entry.studentsTestimonialList.map(testimonialElement => {
  //       const tagList = testimonialElement.tagList.map(el => el.tag)
  //       return {
  //         ...testimonialElement,
  //         tagList,
  //       }
  //     })
  //     return {
  //       ...entry,
  //       slug: 'membership',
  //       id: 'membership',
  //       plans,
  //       membersTestimonialList,
  //       studentsTestimonialList,
  //     }
  //   },
  // },
  // community: {
  //   indexName: 'pages',
  //   entriesQuery: {
  //     populate: [
  //       'header',
  //       'presentation.picture',
  //       'membersBlogHeader',
  //       'membersTestimonialHeader',
  //       'membersTestimonialList.picture',
  //       'topics.ProgrammeListElement.picture',
  //       'bottomCenteredSection.picture',
  //       'seo.metaImage',
  //       'seo.metaSocial.image',
  //     ],
  //   },
  //   async transformEntry({ entry }) {
  //     return {
  //       ...entry,
  //       topics: entry.topics.ProgrammeListElement,
  //       slug: 'community',
  //       id: 'community',
  //     }
  //   },
  // },
  // bursary: {
  //   indexName: 'pages',
  //   entriesQuery: {
  //     populate: [
  //       'header',
  //       'faq.FAQ',
  //       'htmlEmbed',
  //       'seo.metaImage',
  //       'seo.metaSocial.image',
  //     ],
  //   },
  //   async transformEntry({ entry }) {
  //     return {
  //       ...entry,
  //       slug: 'bursary',
  //       id: 'bursary',
  //     }
  //   },
  // },
  // testimonial: {
  //   indexName: 'pages',
  //   entriesQuery: {
  //     populate: [
  //       'header',
  //       'htmlEmbed',
  //       'seo.metaImage',
  //       'seo.metaSocial.image',
  //     ],
  //   },
  //   async transformEntry({ entry }) {
  //     return {
  //       ...entry,
  //       slug: 'testimonial',
  //       id: 'testimonial',
  //     }
  //   },
  // },
  "learn-landing": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'header',
        'headerPublic',
        'headerLinks',
        'featuredWebinarsHeader',
        'featuredWebinarsList.webinar.thumbnail',
        'featuredWebinarsList.webinar.picture',
        'featuredWebinarsList.webinar.TeacherList.teachers.picture',
        'featuredWebinarsList.webinar.TeacherList.teachers.cover',
        'featuredWebinarsList.webinar.TeacherList.teachers.relatedCourses',
        'featuredCoursesHeader',
        'featuredCoursesList.course.thumbnail',
        'featuredCoursesList.course.picture',
        'featuredCoursesList.course.modules.sessions.teachersList.teachers.cover',
        'featuredCoursesList.course.modules.sessions.teachersList.teachers.picture',
        'featuredCoursesList.course.modules.sessions.teachersList.teachers.relatedCourses',
        'featuredLiveConversationsHeader',
        'featuredLiveConversationsList.course_session.thumbnail',
        'featuredLiveConversationsList.course_session.picture',
        'featuredLiveConversationsList.course_session.course.slug',
        'featuredLiveConversationsList.course_session.teachersList.teachers.cover',
        'featuredLiveConversationsList.course_session.teachersList.teachers.picture',
        'featuredLiveConversationsList.course_session.teachersList.teachers.relatedCourses',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      let featuredCoursesList = [],
          featuredWebinarsList = [],
          featuredLiveConversationsList = []

      // flatten upcoming courses
      for (let c of entry.featuredCoursesList) {
        const teachers = c.course?.modules? aggregateModuleTeachers(c.course.modules) : []
        delete c.course?.modules
        delete c.course?.createdAt
        delete c.course?.updatedAt
        delete c.course?.publishedAt

        featuredCoursesList.push({
          contentType: 'courses',
          directLink: `courses/${c.course.slug}`,
          ...c.course,
          teachers })
      }

      // flatten webinars list
      for (let w of entry.featuredWebinarsList) {
        const teachers = mapTeachers(w.webinar?.TeacherList)
        delete w.webinar?.TeacherList
        delete w.webinar?.createdAt
        delete w.webinar?.updatedAt
        delete w.webinar?.publishedAt

        featuredWebinarsList.push({
          contentType: 'webinars',
          directLink: `webinars/${w.webinar.slug}`,
          ...w.webinar,
          teachers })
      }

      // flatten featured live conversations
      for (let c of entry.featuredLiveConversationsList) {
        const teachers = mapTeachers(c.course_session?.teachersList)
        delete c.course_session?.teachersList
        delete c.course_session?.createdAt
        delete c.course_session?.updatedAt
        delete c.course_session?.publishedAt

        featuredLiveConversationsList.push({
          contentType: 'q-and-a',
          ...c.course_session,
          teachers,
          seriesSlug: c.course_session.course?.slug,
          directLink: `courses/${c.course_session.course?.slug}/${c.course_session.slug}`,
        })
      }

      return {
        ...entry,
        slug: 'learn-landing',
        id: 'learn-landing',
        featuredCoursesList,
        featuredWebinarsList,
        featuredLiveConversationsList,
      }
    },
  },
  "practice-landing": {
    indexName: 'pages',
    entriesQuery: {
      populate: [
        'header',
        'headerPublic',
        'headerLinks',
        'featuredClubsHeader',
        'featuredClubsList.eventSession.thumbnail',
        'featuredClubsList.eventSession.picture',
        'featuredClubsList.eventSession.videoURL',
        'featuredClubsList.eventSession.teachersList.teachers.picture',
        'featuredClubsList.eventSession.teachersList.teachers.cover',
        'featuredClubsList.eventSession.event.picture',
        'featuredClubsList.eventSession.event.teachers.picture',
        'featuredClubsList.eventSession.event.teachers.cover',
        'featuredClubsList.eventSession.event.modules.sessions.teachersList.teachers.picture',
        'featuredClubsList.eventSession.event.modules.sessions.teachersList.teachers.cover',
        'audioJourneysHeader',
        'featuredAudioJourneysHeader',
        'featuredAudioJourneysList.eventSeries.thumbnail',
        'featuredAudioJourneysList.eventSeries.picture',
        'featuredAudioJourneysList.eventSeries.modules.sessions.videoURL',
        'featuredAudioJourneysList.eventSeries.modules.sessions.teachersList.teachers.cover',
        'featuredAudioJourneysList.eventSeries.modules.sessions.teachersList.teachers.picture',
        'seo.metaImage',
        'seo.metaSocial.image',
      ],
    },
    async transformEntry({ entry }) {
      // remap header links
      for (let header of ['featuredClubsHeader', 'featuredAudioJourneysHeader'])
        if (entry[header]) entry[header] = mapHeader(entry[header])

      let featuredClubsList = [],
          featuredAudioJourneysList = []

      // flatten clubs list
      for (let e of entry.featuredClubsList) {
        if (e.eventSession.event) {
          featuredClubsList.push({
            directLink: `clubs/${e.eventSeries?.event?.slug}/${e.slug}`,
            duration: e.eventSession.videoURL?.duration || e.eventSession.videoURL?.asset_data?.duration || null,
            ...mapEventSessions(e, 'clubs'),
          })
        }
      }

      // flatten audio journeys list
      for (let c of entry.featuredAudioJourneysList) {
        let teacherList = []
        let duration = null
        c.eventSeries?.modules?.map(module => {
          module.sessions?.map(session => {
            // gather all speakers inside event
            session.teachersList?.teachers.map(teacher => {
              const teacherObj = {
                id: teacher.id,
                name: teacher.name,
                slug: teacher.slug,
                thumbnail: teacher.picture,
                picture: teacher.cover,
              }
              teacherList.push(teacherObj)
            })
            duration = session.videoURL?.duration || session.videoURL?.asset_data?.duration || null
          })
          module.chapters = module.sessions
          delete module.sessions
        })
        delete c.eventSeries?.modules
        delete c.eventSeries?.about
        delete c.eventSeries?.createdAt
        delete c.eventSeries?.updatedAt
        delete c.eventSeries?.publishedAt

        featuredAudioJourneysList.push({
          contentType: 'audio-journeys',
          directLink: `audio-journeys/${c.eventSeries?.slug}`,
          duration,
          ...c.eventSeries,
          teachers: cleanDuplicates(teacherList),
        })
      }

      return {
        ...entry,
        slug: 'practice-landing',
        id: 'practice-landing',
        featuredClubsList,
        featuredAudioJourneysList,
      }
    },
  },
}
