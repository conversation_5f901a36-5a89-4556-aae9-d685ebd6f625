module.exports = {
  async bootstrap() {
    // This will run after the database is fully initialized
    await this.up();
  },

  async up() {
    await strapi.db.transaction(async () => {
      const contentTypeAPI = {
        articles: 'api::read-entry.read-entry',
        films: 'api::watch-entry.watch-entry',
        podcasts: 'api::listen-entry.listen-entry',
        teachers: 'api::entity.entity',
        courses: 'api::course.course',
        courseSessions: 'api::course-session.course-session',
        events: 'api::event.event',
        eventSessions: 'api::event-session.event-session',
        webinars: 'api::webinar.webinar',
      }

      for (const collection of Object.keys(contentTypeAPI)) {
        try {
          // Check if the content type exists and has the cardTitle field
          const contentType = strapi.contentTypes[contentTypeAPI[collection]];
          if (!contentType || !contentType.attributes.cardTitle) {
            console.log(`Skipping ${collection} - cardTitle field not found`);
            continue;
          }

          const contents = await strapi.entityService.findMany(contentTypeAPI[collection], {})
          console.log(`Processing ${contents.length} ${collection}...`);

          for (const content of contents) {
            if (!content.cardTitle) {
              const sourceField = collection === 'teachers' ? content.name : content.title;
              if (sourceField) {
                await strapi.entityService.update(contentTypeAPI[collection], content.id, {
                  data: {
                    cardTitle: sourceField
                  }
                });
                console.log(`Updated cardTitle for ${collection} ${content.id}: "${sourceField}"`);
              }
            }
          }
        } catch (error) {
          console.error(`Error processing ${collection}:`, error.message);
          // Continue with other collections even if one fails
          continue;
        }
      }

      console.log('Copy title to cardTitle migration completed successfully');
    });
  },
};
