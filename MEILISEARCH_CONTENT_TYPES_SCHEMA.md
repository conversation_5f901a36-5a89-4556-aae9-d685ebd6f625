# Meilisearch Content Types Schema Documentation

This document provides frontend developers with the data structure for all content types available through Meilisearch search and filtering. Each schema shows the actual data structure you'll receive when querying these content types.

## Collections

### 1. Subscription Plans (`plans` index)

**Description:** Membership subscription plans with pricing and features

**Data Structure:**
```json
{
  "id": "string",
  "planId": "string",
  "promoId": "string",
  "name": "string",
  "description": "string",
  "planPrice": "number",
  "promoPrice": "number",
  "picture": {
    "url": "string",
    "alternativeText": "string",
    "width": "number",
    "height": "number"
  },
  "featureList": [
    {
      "feature": "string",
      "description": "string"
    }
  ]
}
```

**Search Fields:** `planId`, `promoId`, `name`
**Filter Fields:** `planId`, `planPrice`, `promoId`, `promoPrice`

---

### 2. Category Groups (`category-groups` index)

**Description:** Organized category collections with color coding

**Data Structure:**
```json
{
  "id": "string",
  "slug": "string",
  "title": "string",
  "color": "string",
  "categories": ["string"],
  "hubspot_list_id": "string"
}
```

**Search Fields:** `title`
**Filter Fields:** `title`, `slug`

---

### 3. Categories (`categories` index)

**Description:** Content categorization tags

**Data Structure:**
```json
{
  "id": "string",
  "title": "string",
  "hubspot_list_id": "string",
  "slug": "string"
}
```

**Search Fields:** `title`
**Filter Fields:** `title`, `slug`

---

### 4. Teachers (`entities` index)

**Description:** Faculty members with profiles and related content

**Data Structure:**
```json
{
  "id": "string",
  "name": "string",
  "cardTitle": "string",
  "description": "string",
  "bio": "string",
  "ranking": "number",
  "slug": "string",
  "user_id": "string",
  "user_email": "string",
  "picture": {
    "url": "string",
    "alternativeText": "string",
    "width": "number",
    "height": "number"
  },
  "cover": {
    "url": "string",
    "alternativeText": "string",
    "width": "number",
    "height": "number"
  },
  "Related": [
    {
      "contentType": "string",
      "course": "object",
      "event": "object",
      "article": "object",
      "film": "object",
      "podcast": "object"
    }
  ],
  "relatedCourses": ["object"],
  "relatedEvents": ["object"],
  "relatedArticles": ["object"],
  "relatedFilms": ["object"],
  "relatedPodcasts": ["object"],
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "metaImage": "object"
  }
}
```

**Search Fields:** `name`, `description`, `bio`
**Filter Fields:** `name`, `description`, `bio`
**Sort Fields:** `ranking`, `name`

---

### 5. Course Lessons (`lessons` index)

**Description:** Individual chapters/lessons within courses

**Data Structure:**
```json
{
  "id": "string",
  "title": "string",
  "cardTitle": "string",
  "description": "string",
  "about": "string",
  "slug": "string",
  "startDateTime": "string",
  "endDateTime": "string",
  "startDateTimeTimestamp": "number",
  "endDateTimeTimestamp": "number",
  "liveURL": "string",
  "moduleTitle": "string",
  "moduleIndex": "number",
  "chapterIndexInModule": "number",
  "contentType": "string",
  "videoURL": {
    "playbackId": "string",
    "assetId": "string"
  },
  "audioURL": {
    "playbackId": "string",
    "assetId": "string"
  },
  "picture": {
    "url": "string",
    "alternativeText": "string"
  },
  "thumbnail": {
    "url": "string",
    "alternativeText": "string"
  },
  "teachers": [
    {
      "name": "string",
      "slug": "string"
    }
  ],
  "teachersList": {
    "teachers": [
      {
        "name": "string",
        "slug": "string"
      }
    ]
  },
  "transcript": {
    "title": "string",
    "attachment": "object"
  },
  "readingResources": [
    {
      "title": "string",
      "description": "string",
      "attachment": "object",
      "url": "string",
      "content": "string"
    }
  ],
  "practices": [
    {
      "title": "string",
      "description": "string",
      "attachment": "object"
    }
  ]
}
```

**Search Fields:** `title`, `description`
**Filter Fields:** `id`, `slug`, `startDateTime`, `startDateTimeTimestamp`, `endDateTimeTimestamp`, `teachers.slug`, `teachers.name`, `teachersList.teachers.slug`, `teachersList.teachers.name`, `q-and-a`, `contentType`
**Sort Fields:** `startDateTime`

---

### 6. Courses (`courses` index)

**Description:** Educational courses with modules and sessions

**Data Structure:**
```json
{
  "id": "string",
  "title": "string",
  "cardTitle": "string",
  "headline": "string",
  "dateHeadline": "string",
  "description": "string",
  "about": "string",
  "price": "number",
  "slug": "string",
  "startDateTime": "string",
  "endDateTime": "string",
  "startDateTimeTimestamp": "number",
  "endDateTimeTimestamp": "number",
  "chargebee_entity_id": "string",
  "hubspot_list_id": "string",
  "discussionId": "string",
  "thumbnail": [
    {
      "url": "string",
      "alternativeText": "string"
    }
  ],
  "picture": {
    "url": "string",
    "alternativeText": "string"
  },
  "categories": [
    {
      "slug": "string",
      "title": "string"
    }
  ],
  "teachers": [
    {
      "name": "string",
      "slug": "string"
    }
  ],
  "modules": [
    {
      "title": "string",
      "headline": "string",
      "description": "string",
      "teachers": [
        {
          "name": "string",
          "slug": "string"
        }
      ]
    }
  ],
  "Curriculum": {
    "title": "string",
    "KeyTakeaways": [
      {
        "description": "string"
      }
    ]
  },
  "readingResources": [
    {
      "title": "string",
      "description": "string",
      "attachment": "object",
      "url": "string",
      "content": "string"
    }
  ],
  "Related": [
    {
      "contentType": "string",
      "course": "object",
      "event": "object",
      "article": "object"
    }
  ]
}
```

**Search Fields:** `title`, `headline`, `description`, `about`
**Filter Fields:** `id`, `categories.slug`, `categories.title`, `modules.teachers.slug`, `modules.teachers.name`, `teachers.slug`, `teachers.name`, `price`, `dateHeadline`, `startDateTime`, `startDateTimeTimestamp`, `endDateTimeTimestamp`, `endDateTime`, `chargebee_entity_id`, `slug`
**Sort Fields:** `startDateTime`

---

### 7. Event Sessions (`sessions` index)

**Description:** Individual sessions within events (journeys & club sessions)

**Data Structure:**
```json
{
  "id": "string",
  "title": "string",
  "cardTitle": "string",
  "description": "string",
  "about": "string",
  "headline": "string",
  "dateHeadline": "string",
  "slug": "string",
  "startDateTime": "string",
  "endDateTime": "string",
  "startDateTimeTimestamp": "number",
  "endDateTimeTimestamp": "number",
  "liveURL": "string",
  "contentType": "string",
  "videoURL": {
    "playbackId": "string",
    "assetId": "string"
  },
  "audioURL": {
    "playbackId": "string",
    "assetId": "string"
  },
  "picture": {
    "url": "string",
    "alternativeText": "string"
  },
  "thumbnail": {
    "url": "string",
    "alternativeText": "string"
  },
  "teachers": [
    {
      "name": "string",
      "slug": "string"
    }
  ],
  "teachersList": {
    "teachers": [
      {
        "name": "string",
        "slug": "string"
      }
    ]
  },
  "categories": [
    {
      "slug": "string",
      "title": "string"
    }
  ]
}
```

**Search Fields:** `title`, `description`
**Filter Fields:** `id`, `slug`, `startDateTime`, `teachers.slug`, `teachers.name`, `teachersList.teachers.slug`, `teachersList.teachers.name`, `categories.slug`, `dateHeadline`, `startDateTimeTimestamp`, `endDateTimeTimestamp`, `headline`, `contentType`
**Sort Fields:** `startDateTime`

---

### 8. Events (`events` index)

**Description:** Audio journeys and clubs with multiple sessions

**Data Structure:**
```json
{
  "id": "string",
  "title": "string",
  "cardTitle": "string",
  "headline": "string",
  "description": "string",
  "about": "string",
  "dateHeadline": "string",
  "price": "number",
  "slug": "string",
  "startDateTime": "string",
  "endDateTime": "string",
  "startDateTimeTimestamp": "number",
  "endDateTimeTimestamp": "number",
  "chargebee_entity_id": "string",
  "hubspot_list_id": "string",
  "recurring": "boolean",
  "thumbnail": {
    "url": "string",
    "alternativeText": "string"
  },
  "picture": {
    "url": "string",
    "alternativeText": "string"
  },
  "categories": [
    {
      "slug": "string",
      "title": "string"
    }
  ],
  "teachers": [
    {
      "name": "string",
      "slug": "string"
    }
  ],
  "modules": [
    {
      "title": "string",
      "description": "string",
      "teachers": [
        {
          "name": "string",
          "slug": "string"
        }
      ]
    }
  ],
  "Curriculum": {
    "title": "string",
    "KeyTakeaways": [
      {
        "description": "string"
      }
    ]
  }
}
```

**Search Fields:** `title`, `headline`, `description`, `about`
**Filter Fields:** `id`, `categories.slug`, `categories.title`, `teachers.slug`, `teachers.name`, `modules.teachers.slug`, `modules.teachers.name`, `price`, `dateHeadline`, `startDateTime`, `startDateTimeTimestamp`, `endDateTimeTimestamp`, `endDateTime`, `chargebee_entity_id`, `recurring`, `slug`
**Sort Fields:** `startDateTime`

---

### 9. Articles (`articles` index)

**Description:** Magazine articles and written content

**Data Structure:**
```json
{
  "id": "string",
  "title": "string",
  "cardTitle": "string",
  "description": "string",
  "content": "string",
  "date": "string",
  "slug": "string",
  "price": "number",
  "chargebee_entity_id": "string",
  "thumbnail": {
    "url": "string",
    "alternativeText": "string"
  },
  "picture": {
    "url": "string",
    "alternativeText": "string"
  },
  "categories": [
    {
      "slug": "string",
      "title": "string"
    }
  ],
  "teachers": [
    {
      "name": "string",
      "slug": "string"
    }
  ],
  "TeacherList": {
    "teachers": [
      {
        "name": "string",
        "slug": "string"
      }
    ]
  },
  "readingResources": [
    {
      "title": "string",
      "description": "string",
      "attachment": "object",
      "url": "string",
      "content": "string"
    }
  ],
  "Related": [
    {
      "contentType": "string",
      "course": "object",
      "event": "object",
      "article": "object"
    }
  ]
}
```

**Search Fields:** `title`, `description`, `content`
**Filter Fields:** `categories.slug`, `categories.title`, `teachers.slug`, `teachers.name`, `TeacherList.teachers.slug`, `TeacherList.teachers.name`, `price`, `chargebee_entity_id`
**Sort Fields:** `date`

---

### 10. Podcasts (`podcasts` index)

**Description:** Podcast episodes and audio content

**Data Structure:**
```json
{
  "id": "string",
  "title": "string",
  "cardTitle": "string",
  "description": "string",
  "about": "string",
  "date": "string",
  "slug": "string",
  "price": "number",
  "chargebee_entity_id": "string",
  "thumbnail": {
    "url": "string",
    "alternativeText": "string"
  },
  "picture": {
    "url": "string",
    "alternativeText": "string"
  },
  "audioURL": {
    "playbackId": "string",
    "assetId": "string"
  },
  "categories": [
    {
      "slug": "string",
      "title": "string"
    }
  ],
  "teachers": [
    {
      "name": "string",
      "slug": "string"
    }
  ],
  "TeacherList": {
    "teachers": [
      {
        "name": "string",
        "slug": "string"
      }
    ]
  },
  "readingResources": [
    {
      "title": "string",
      "description": "string",
      "attachment": "object",
      "url": "string"
    }
  ],
  "Related": [
    {
      "contentType": "string",
      "course": "object",
      "event": "object",
      "podcast": "object"
    }
  ]
}
```

**Search Fields:** `title`, `description`, `about`
**Filter Fields:** `categories.slug`, `categories.title`, `teachers.slug`, `teachers.name`, `TeacherList.teachers.slug`, `TeacherList.teachers.name`, `price`, `chargebee_entity_id`, `slug`
**Sort Fields:** `date`

---

### 11. Webinars (`webinars` index)

**Description:** Live webinar sessions and recordings

**Data Structure:**
```json
{
  "id": "string",
  "slug": "string",
  "title": "string",
  "cardTitle": "string",
  "dateHeadline": "string",
  "description": "string",
  "about": "string",
  "price": "number",
  "startDateTime": "string",
  "endDateTime": "string",
  "startDateTimeTimestamp": "number",
  "endDateTimeTimestamp": "number",
  "timezone": "string",
  "registrationFormText": "string",
  "chargebee_entity_id": "string",
  "hubspot_list_id": "string",
  "zoomURL": "string",
  "zoomMeetingNumber": "string",
  "zoomMeetingPassword": "string",
  "picture": {
    "url": "string",
    "alternativeText": "string"
  },
  "thumbnail": {
    "url": "string",
    "alternativeText": "string"
  },
  "videoURL": {
    "playbackId": "string",
    "assetId": "string"
  },
  "categories": [
    {
      "slug": "string",
      "title": "string"
    }
  ],
  "teachers": [
    {
      "name": "string",
      "slug": "string"
    }
  ],
  "TeacherList": {
    "teachers": [
      {
        "name": "string",
        "slug": "string"
      }
    ]
  },
  "Curriculum": {
    "title": "string",
    "KeyTakeaways": [
      {
        "description": "string"
      }
    ]
  },
  "Related": [
    {
      "contentType": "string",
      "webinar": "object",
      "course": "object"
    }
  ]
}
```

**Search Fields:** `title`, `description`, `about`
**Filter Fields:** `categories.slug`, `categories.title`, `teachers.slug`, `teachers.name`, `TeacherList.teachers.slug`, `TeacherList.teachers.name`, `startDateTime`, `endDateTime`, `startDateTimeTimestamp`, `endDateTimeTimestamp`, `price`, `chargebee_entity_id`, `slug`
**Sort Fields:** `startDateTime`

## Pages (`pages` index)

All page content types use the same index and represent static website pages.

### 1. Homepage

**Description:** Main landing page with featured content sections

**Data Structure:**
```json
{
  "id": "homepage",
  "slug": "homepage",
  "header": {
    "title": "string",
    "description": "string",
    "thumbnail": {
      "url": "string",
      "alternativeText": "string"
    },
    "previewVideo": {
      "playbackId": "string",
      "assetId": "string"
    }
  },
  "intro": {
    "description": "string",
    "header": {
      "title": "string",
      "description": "string"
    },
    "videoList": [
      {
        "video": "object",
        "thumbnail": "object"
      }
    ]
  },
  "learnHeader": {
    "title": "string",
    "description": "string"
  },
  "featuredWebinarsHeader": {
    "title": "string",
    "description": "string"
  },
  "featuredWebinarsList": [
    {
      "webinar": "object"
    }
  ],
  "upcomingCoursesHeader": {
    "title": "string",
    "description": "string"
  },
  "upcomingCoursesList": [
    {
      "course": "object"
    }
  ],
  "featuredClubsHeader": {
    "title": "string",
    "description": "string"
  },
  "featuredClubsList": [
    {
      "eventSession": "object"
    }
  ],
  "teachersHeader": {
    "title": "string",
    "description": "string"
  },
  "teachersList": {
    "teachers": ["object"]
  },
  "membershipHeader": {
    "title": "string"
  },
  "membershipList": {
    "items": ["object"]
  },
  "testimonialsHeader": {
    "title": "string"
  },
  "testimonialsList": [
    {
      "name": "string",
      "testimonial": "string",
      "picture": "object"
    }
  ],
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "metaImage": "object"
  }
}
```

---

### 2. About Us

**Description:** Team and company information page

**Data Structure:**
```json
{
  "id": "about-us",
  "slug": "about-us",
  "header": {
    "title": "string",
    "picture": {
      "url": "string",
      "alternativeText": "string"
    }
  },
  "content": {
    "about": "string"
  },
  "ourThoughtsHeader": {
    "title": "string"
  },
  "ourThoughtsList": {
    "contentType": "articles",
    "items": ["object"]
  },
  "features": [
    {
      "title": "string",
      "description": "string",
      "content": "string"
    }
  ],
  "team": {
    "TeamMemberElements": [
      {
        "name": "string",
        "role": "string",
        "picture": "object"
      }
    ]
  },
  "ourProgrammesHeader": {
    "title": "string"
  },
  "ourProgrammesList": ["object"],
  "topicsHeader": {
    "title": "string"
  },
  "topicsList": ["object"],
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "metaImage": "object"
  }
}
```

---

### 3. Partners

**Description:** Partners and collaborations page

**Data Structure:**
```json
{
  "id": "partners",
  "slug": "partners",
  "title": "Partners",
  "description": "string",
  "content": "string",
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "metaImage": "object"
  }
}
```

---

### 4. Teachers

**Description:** Faculty landing page

**Data Structure:**
```json
{
  "id": "teachers",
  "slug": "teachers",
  "header": {
    "title": "string",
    "description": "string"
  },
  "featuredTeachersHeader": {
    "title": "string",
    "description": "string"
  },
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "metaImage": "object"
  }
}
```

---

### 5. FAQ and Contact Us

**Description:** Frequently asked questions and contact information

**Data Structure:**
```json
{
  "id": "faq-and-contact-us",
  "slug": "faq-and-contact-us",
  "header": {
    "title": "string",
    "picture": {
      "url": "string",
      "alternativeText": "string"
    }
  },
  "faq": {
    "FAQ": [
      {
        "question": "string",
        "answer": "string"
      }
    ]
  },
  "contact": {
    "title": "string",
    "description": "string",
    "email": "string",
    "phone": "string"
  },
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "metaImage": "object"
  }
}
```

---

### 6. Contact

**Description:** Contact information and form page

**Data Structure:**
```json
{
  "id": "contact",
  "slug": "contact",
  "title": "Contact",
  "description": "string",
  "contact": {
    "title": "string",
    "description": "string",
    "email": "string",
    "phone": "string"
  },
  "content": "string",
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "metaImage": "object"
  }
}
```

---

### 7. Philosophy

**Description:** Company philosophy and values page

**Data Structure:**
```json
{
  "id": "philosophy",
  "slug": "philosophy",
  "title": "Philosophy",
  "description": "string",
  "content": "string",
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "metaImage": "object"
  }
}
```

---

### 8. Vacancies

**Description:** Job openings and career opportunities

**Data Structure:**
```json
{
  "id": "vacancies",
  "slug": "vacancies",
  "title": "Vacancies",
  "description": "string",
  "content": "string",
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "metaImage": "object"
  }
}
```

---

### 9. The Method

**Description:** Methodology and approach explanation

**Data Structure:**
```json
{
  "id": "the-method",
  "slug": "the-method",
  "title": "The Method",
  "description": "string",
  "content": "string",
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "metaImage": "object"
  }
}
```

---

### 10. Affiliates

**Description:** Affiliate program information

**Data Structure:**
```json
{
  "id": "affiliates",
  "slug": "affiliates",
  "title": "Affiliates",
  "description": "string",
  "content": "string",
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "metaImage": "object"
  }
}
```

---

### 11. About

**Description:** General about page

**Data Structure:**
```json
{
  "id": "about",
  "slug": "about",
  "title": "About",
  "description": "string",
  "content": "string",
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "metaImage": "object"
  }
}
```

---

### 12. Practice Landing

**Description:** Practice section landing page with clubs and audio journeys

**Data Structure:**
```json
{
  "id": "practice-landing",
  "slug": "practice-landing",
  "headerPublic": {
    "title": "string",
    "subtitle": "string"
  },
  "header": {
    "title": "string",
    "subtitle": "string"
  },
  "headerLinks": [
    {
      "title": "string",
      "url": "string"
    }
  ],
  "featuredClubsHeader": {
    "title": "string",
    "description": "string"
  },
  "featuredClubsList": [
    {
      "eventSession": "object"
    }
  ],
  "audioJourneysHeader": {
    "title": "string",
    "subtitle": "string"
  },
  "featuredAudioJourneysHeader": {
    "title": "string",
    "description": "string"
  },
  "featuredAudioJourneysList": [
    {
      "event": "object"
    }
  ],
  "seo": {
    "metaTitle": "string",
    "metaDescription": "string",
    "metaImage": "object"
  }
}
```

## Data Types Reference

### Media Object Structure
```json
{
  "url": "string",
  "alternativeText": "string",
  "width": "number",
  "height": "number",
  "size": "number",
  "mime": "string"
}
```

### Video/Audio Asset Structure (Mux)
```json
{
  "playbackId": "string",
  "assetId": "string",
  "duration": "number",
  "aspectRatio": "string"
}
```

### SEO Object Structure
```json
{
  "metaTitle": "string",
  "metaDescription": "string",
  "metaImage": "object",
  "metaSocial": [
    {
      "socialNetwork": "Facebook|Twitter",
      "title": "string",
      "description": "string",
      "image": "object"
    }
  ],
  "keywords": "string",
  "metaRobots": "string",
  "structuredData": "object",
  "metaViewport": "string",
  "canonicalURL": "string"
}
```

## Usage Notes for Frontend Developers

1. **Search Implementation**: Use the "Search Fields" to implement full-text search functionality
2. **Filtering**: Use "Filter Fields" for faceted search and filtering interfaces
3. **Sorting**: Use "Sort Fields" for ordering results (e.g., by date, price, ranking)
4. **Media Handling**: All media objects include URLs and metadata for responsive images
5. **Video/Audio**: Mux assets provide playback IDs for streaming media
6. **SEO**: All content types include SEO metadata for meta tags and social sharing
7. **Timestamps**: Date fields include both human-readable strings and Unix timestamps for easy sorting/filtering
8. **Related Content**: Use the "Related" arrays to build content recommendation features
9. **Teachers**: Teacher data is consistently structured across all content types for unified faculty displays
10. **Categories**: Category arrays enable tag-based filtering and content organization
