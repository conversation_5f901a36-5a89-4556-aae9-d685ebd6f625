# Meilisearch Content Types Schema Documentation

This document provides a comprehensive overview of all content types mapped in the Meilisearch configuration files (`config/meilisearch/pages.js` and `config/meilisearch/collections.js`), including their schemas, filterable attributes, sortable attributes, and field descriptions.

## Collections (config/meilisearch/collections.js)

### 1. Subscription Plan (`subscription-plan`)

**Index Name:** `plans`

**Description:** Membership subscription plans for the platform

**Schema:**
```json
{
  "planId": "string (required)",
  "promoId": "string (required)", 
  "name": "string (required)",
  "description": "string (required)",
  "planPrice": "decimal (required, min: 0, default: 0)",
  "promoPrice": "decimal (required, min: 0, default: 0)",
  "picture": "media (images, optional)",
  "slug": "uid (auto-generated from planId)",
  "featureList": "component[] (feature-list, required, min: 1)"
}
```

**Searchable Attributes:** `planId`, `promoId`, `name`
**Filterable Attributes:** `planId`, `planPrice`, `promoId`, `promoPrice`
**Sortable Attributes:** None

---

### 2. Category Groups (`category-groups`)

**Index Name:** `category-groups`

**Description:** Groups that organize categories with color coding

**Schema:**
```json
{
  "title": "string (required)",
  "slug": "uid (auto-generated from title, required)",
  "colour": "string (required, default: '#000000')",
  "categories": "relation (oneToMany to category)",
  "hubspot_list_id": "string (optional)"
}
```

**Searchable Attributes:** `title`
**Filterable Attributes:** `title`, `slug`
**Sortable Attributes:** None

---

### 3. Category (`category`)

**Index Name:** `categories`

**Description:** Content categorization system

**Schema:**
```json
{
  "title": "string (required)",
  "slug": "uid (auto-generated from title, required)",
  "hubspot_list_id": "string (optional)"
}
```

**Searchable Attributes:** `title`
**Filterable Attributes:** `title`, `slug`
**Sortable Attributes:** None

---

### 4. Entity (`entity`)

**Index Name:** `entities`

**Description:** Teachers/Faculty members with their profiles and related content

**Schema:**
```json
{
  "name": "string (required)",
  "cardTitle": "string (optional)",
  "description": "text (required)",
  "picture": "media (images, optional)",
  "cover": "media (images, optional)",
  "bio": "richtext (required)",
  "Related": "dynamiczone (related content, max: 5, min: 1)",
  "slug": "uid (auto-generated from name, required)",
  "seo": "component (shared.seo, optional)",
  "ranking": "integer (0-1000, required, default: 0)",
  "user_id": "string (optional)",
  "user_email": "string (email format, optional)",
  "relatedCourses": "relation (oneToMany to course)",
  "relatedEvents": "relation (oneToMany to event)",
  "relatedArticles": "relation (oneToMany to read-entry)",
  "relatedFilms": "relation (oneToMany to watch-entry)",
  "relatedPodcasts": "relation (oneToMany to listen-entry)",
  "relatedWebinars": "relation (oneToMany to webinar)"
}
```

**Searchable Attributes:** `name`, `description`, `bio`
**Filterable Attributes:** `name`, `description`, `bio`
**Sortable Attributes:** `ranking`, `name`

---

### 5. Course Session (`course-session`)

**Index Name:** `lessons`

**Description:** Individual chapters/lessons within courses

**Schema:**
```json
{
  "title": "string (required)",
  "cardTitle": "string (optional)",
  "description": "text (required)",
  "about": "richtext (optional)",
  "liveURL": "string (optional)",
  "startDateTime": "datetime (optional)",
  "endDateTime": "datetime (optional)",
  "course": "relation (oneToOne to course)",
  "slug": "uid (auto-generated from title, required)",
  "videoURL": "relation (oneToOne to mux-asset)",
  "videoThumbnail": "media (images, optional)",
  "videoTimecode": "component (video-timecode, optional)",
  "audioURL": "relation (oneToOne to mux-asset)",
  "transcript": "component (file-element, optional)",
  "picture": "media (images, optional)",
  "thumbnail": "media (images, optional)",
  "teachersList": "component (speaker-list, optional)",
  "readingResources": "dynamiczone (file/hyperlink/text elements)",
  "practices": "dynamiczone (file/hyperlink/text elements)",
  "seo": "component (shared.seo, optional)",
  "moduleTitle": "string (optional)",
  "moduleIndex": "integer (default: 0)",
  "chapterIndexInModule": "integer (default: 0)"
}
```

**Searchable Attributes:** `title`, `description`
**Filterable Attributes:** `id`, `slug`, `startDateTime`, `startDateTimeTimestamp`, `endDateTimeTimestamp`, `teachers.slug`, `teachers.name`, `teachersList.teachers.slug`, `teachersList.teachers.name`, `q-and-a`, `contentType`
**Sortable Attributes:** `startDateTime`

---

### 6. Course (`course`)

**Index Name:** `courses`

**Description:** Educational courses with modules and sessions

**Schema:**
```json
{
  "title": "string (required)",
  "cardTitle": "string (optional)",
  "headline": "string (optional)",
  "dateHeadline": "string (optional)",
  "description": "text (required)",
  "thumbnail": "media (images, multiple, optional)",
  "picture": "media (images, required)",
  "about": "richtext (optional)",
  "price": "decimal (required, default: 0)",
  "startDateTime": "datetime (required)",
  "endDateTime": "datetime (optional)",
  "Curriculum": "component (curriculum, required)",
  "modules": "component[] (course-module)",
  "liveConversations": "component (course-module)",
  "Facilitators": "component (facilitator-list)",
  "InfoBox": "component (information-box, required)",
  "categories": "relation (oneToMany to category)",
  "slug": "uid (auto-generated from title, required)",
  "teachers": "relation (oneToMany to entity)",
  "chargebee_entity_id": "string (optional)",
  "readingResources": "dynamiczone (file/hyperlink/text elements)",
  "relatedHeader": "component (simple-header, optional)",
  "Related": "dynamiczone (related content, min: 1)",
  "seo": "component (shared.seo, optional)",
  "hubspot_list_id": "string (optional)",
  "discussionId": "string (optional)"
}
```

**Searchable Attributes:** `title`, `headline`, `description`, `about`
**Filterable Attributes:** `id`, `categories.slug`, `categories.title`, `modules.teachers.slug`, `modules.teachers.name`, `teachers.slug`, `teachers.name`, `price`, `dateHeadline`, `startDateTime`, `startDateTimeTimestamp`, `endDateTimeTimestamp`, `endDateTime`, `chargebee_entity_id`, `slug`
**Sortable Attributes:** `startDateTime`

---

### 7. Event Session (`event-session`)

**Index Name:** `sessions`

**Description:** Individual sessions within events (journeys & club sessions)

**Schema:**
```json
{
  "title": "string (required)",
  "cardTitle": "string (optional)",
  "description": "text (required)",
  "about": "richtext (optional)",
  "liveURL": "string (optional)",
  "dateHeadline": "string (optional)",
  "headline": "string (optional)",
  "startDateTime": "datetime (required)",
  "endDateTime": "datetime (optional)",
  "event": "relation (oneToOne to event)",
  "slug": "uid (auto-generated from title, required)",
  "videoURL": "relation (oneToOne to mux-asset)",
  "videoThumbnail": "media (images, optional)",
  "audioURL": "relation (oneToOne to mux-asset)",
  "transcript": "component (file-element, optional)",
  "picture": "media (images, optional)",
  "thumbnail": "media (images, optional)",
  "teachersList": "component (speaker-list, optional)",
  "readingResources": "dynamiczone (file/hyperlink/text elements)",
  "practices": "dynamiczone (file/hyperlink/text elements)",
  "seo": "component (shared.seo, optional)",
  "moduleTitle": "string (optional)",
  "moduleIndex": "integer (default: 0)",
  "chapterIndexInModule": "integer (default: 0)"
}
```

**Searchable Attributes:** `title`, `description`
**Filterable Attributes:** `id`, `slug`, `startDateTime`, `teachers.slug`, `teachers.name`, `teachersList.teachers.slug`, `teachersList.teachers.name`, `categories.slug`, `dateHeadline`, `startDateTimeTimestamp`, `endDateTimeTimestamp`, `headline`, `contentType`
**Sortable Attributes:** `startDateTime`

---

### 8. Event (`event`)

**Index Name:** `events`

**Description:** Audio journeys and clubs with multiple sessions

**Schema:**
```json
{
  "title": "string (required)",
  "cardTitle": "string (optional)",
  "headline": "string (optional)",
  "description": "text (required)",
  "dateHeadline": "string (optional)",
  "thumbnail": "media (images, optional)",
  "picture": "media (images, required)",
  "about": "richtext (optional)",
  "price": "decimal (required, default: 0)",
  "startDateTime": "datetime (required)",
  "endDateTime": "datetime (optional)",
  "Curriculum": "component (curriculum, required)",
  "modules": "component[] (event-module)",
  "Facilitators": "component (facilitator-list)",
  "InfoBox": "component (information-box, required)",
  "categories": "relation (oneToMany to category)",
  "slug": "uid (auto-generated from title, required)",
  "teachers": "relation (oneToMany to entity)",
  "chargebee_entity_id": "string (optional)",
  "recurring": "boolean (optional)",
  "readingResources": "dynamiczone (file/hyperlink/text elements)",
  "Related": "dynamiczone (related content, min: 1)",
  "seo": "component (shared.seo, optional)",
  "hubspot_list_id": "string (optional)"
}
```

**Searchable Attributes:** `title`, `headline`, `description`, `about`
**Filterable Attributes:** `id`, `categories.slug`, `categories.title`, `teachers.slug`, `teachers.name`, `modules.teachers.slug`, `modules.teachers.name`, `price`, `dateHeadline`, `startDateTime`, `startDateTimeTimestamp`, `endDateTimeTimestamp`, `endDateTime`, `chargebee_entity_id`, `recurring`, `slug`
**Sortable Attributes:** `startDateTime`

---

### 9. Read Entry (`read-entry`)

**Index Name:** `articles`

**Description:** Magazine articles and written content

**Schema:**
```json
{
  "title": "string (required)",
  "cardTitle": "string (optional)",
  "categories": "relation (oneToMany to category)",
  "description": "text (required)",
  "date": "date (required)",
  "thumbnail": "media (images, optional)",
  "picture": "media (images, optional)",
  "about": "richtext (required)",
  "TeacherList": "component (speaker-list, required)",
  "readingResources": "dynamiczone (file/hyperlink/text elements)",
  "Related": "dynamiczone (related content, min: 1)",
  "slug": "uid (auto-generated from title, required)",
  "price": "decimal (required, default: 0)",
  "chargebee_entity_id": "string (optional)",
  "seo": "component (shared.seo, optional)"
}
```

**Searchable Attributes:** `title`, `description`, `content`
**Filterable Attributes:** `categories.slug`, `categories.title`, `teachers.slug`, `teachers.name`, `TeacherList.teachers.slug`, `TeacherList.teachers.name`, `price`, `chargebee_entity_id`
**Sortable Attributes:** `date`

---

### 10. Listen Entry (`listen-entry`)

**Index Name:** `podcasts`

**Description:** Podcast episodes and audio content

**Schema:**
```json
{
  "title": "string (required)",
  "cardTitle": "string (optional)",
  "categories": "relation (oneToMany to category)",
  "thumbnail": "media (images, optional)",
  "picture": "media (images, optional)",
  "description": "text (required)",
  "about": "richtext (optional)",
  "date": "date (required)",
  "audioURL": "relation (oneToOne to mux-asset)",
  "readingResources": "dynamiczone (file/hyperlink/text elements)",
  "Related": "dynamiczone (related content, min: 1)",
  "TeacherList": "component (speaker-list, required)",
  "slug": "uid (auto-generated from title, required)",
  "price": "decimal (required, default: 0)",
  "chargebee_entity_id": "string (optional)",
  "seo": "component (shared.seo, optional)"
}
```

**Searchable Attributes:** `title`, `description`, `about`
**Filterable Attributes:** `categories.slug`, `categories.title`, `teachers.slug`, `teachers.name`, `TeacherList.teachers.slug`, `TeacherList.teachers.name`, `price`, `chargebee_entity_id`, `slug`
**Sortable Attributes:** `date`

---

### 11. Webinar (`webinar`)

**Index Name:** `webinars`

**Description:** Live webinar sessions and recordings

**Schema:**
```json
{
  "slug": "uid (auto-generated from title, required)",
  "price": "decimal (required, default: 0)",
  "chargebee_entity_id": "string (optional)",
  "title": "string (required)",
  "cardTitle": "string (optional)",
  "dateHeadline": "string (optional)",
  "startDateTime": "datetime (required)",
  "endDateTime": "datetime (optional)",
  "timezone": "customField (timezone-select, required)",
  "description": "text (optional)",
  "about": "richtext (required)",
  "registrationFormText": "string (default: 'Register for free to receive joining links')",
  "zoomURL": "string (optional)",
  "zoomMeetingNumber": "string (optional)",
  "zoomMeetingPassword": "string (optional)",
  "picture": "media (images, optional)",
  "thumbnail": "media (images, optional)",
  "Curriculum": "component (curriculum, required)",
  "TeacherList": "component (speaker-list, required)",
  "categories": "relation (oneToMany to category)",
  "Related": "dynamiczone (related content, min: 1)",
  "seo": "component (shared.seo, optional)",
  "hubspot_list_id": "string (optional)",
  "videoURL": "relation (oneToOne to mux-asset)"
}
```

**Searchable Attributes:** `title`, `description`, `about`
**Filterable Attributes:** `categories.slug`, `categories.title`, `teachers.slug`, `teachers.name`, `TeacherList.teachers.slug`, `TeacherList.teachers.name`, `startDateTime`, `endDateTime`, `startDateTimeTimestamp`, `endDateTimeTimestamp`, `price`, `chargebee_entity_id`, `slug`
**Sortable Attributes:** `startDateTime`

## Pages (config/meilisearch/pages.js)

All page content types use the index name `pages` and are single-type entries representing static pages.

### 1. Homepage (`homepage`)

**Description:** Main landing page with featured content sections

**Schema:**
```json
{
  "header": "component (hero-header, required)",
  "intro": "component (homepage-intro, required)",
  "learnHeader": "component (simple-header, required)",
  "featuredWebinarsHeader": "component (promo-section, required)",
  "featuredWebinarsList": "component[] (featured-webinar, required, 4-10 items)",
  "upcomingCoursesHeader": "component (promo-section, required)",
  "upcomingCoursesList": "component[] (featured-course, required, 4-10 items)",
  "featuredClubsHeader": "component (promo-section, required)",
  "featuredClubsList": "component[] (ft-event-session, required, 3-10 items)",
  "teachersHeader": "component (promo-section-desc, required)",
  "teachersList": "component (speaker-list, required)",
  "membershipHeader": "component (simple-header, required)",
  "membershipList": "component (content-list, required)",
  "testimonialsHeader": "component (simple-header, required)",
  "testimonialsList": "component[] (student-testimonial, required, 3-10 items)",
  "seo": "component (shared.seo, required)"
}
```

**Searchable Attributes:** None specified
**Filterable Attributes:** None specified
**Sortable Attributes:** None specified

---

### 2. About Us (`about-us`)

**Description:** Team and company information page

**Schema:**
```json
{
  "header": "component (full-width-header, required)",
  "content": "component (content-text, required)",
  "ourThoughtsHeader": "component (simple-header, required)",
  "ourThoughtsList": "component (article-cards, required)",
  "features": "component[] (content-presentation, required)",
  "team": "component (team-members, required)",
  "ourProgrammesHeader": "component (simple-header, required)",
  "ourProgrammesList": "component[] (programme-list-element)",
  "topicsHeader": "component (simple-header, required)",
  "topicsList": "component[] (programme-list-element)",
  "seo": "component (shared.seo, required)"
}
```

**Searchable Attributes:** None specified
**Filterable Attributes:** None specified
**Sortable Attributes:** None specified

---

### 3. Partners (`partners`)

**Description:** Partners and collaborations page

**Schema:**
```json
{
  "title": "string (required, default: 'Partners')",
  "description": "text (required)",
  "content": "richtext (required)",
  "seo": "component (shared.seo, required)"
}
```

**Searchable Attributes:** None specified
**Filterable Attributes:** None specified
**Sortable Attributes:** None specified

---

### 4. Teachers (`teachers`)

**Description:** Faculty landing page

**Schema:**
```json
{
  "header": "component (simple-header-desc, required)",
  "featuredTeachersHeader": "component (promo-section, required)",
  "seo": "component (shared.seo, required)"
}
```

**Searchable Attributes:** None specified
**Filterable Attributes:** None specified
**Sortable Attributes:** None specified

---

### 5. FAQ and Contact Us (`faq-and-contact-us`)

**Description:** Frequently asked questions and contact information

**Schema:**
```json
{
  "header": "component (simple-header-pic, required)",
  "faq": "component (faq-list, required)",
  "contact": "component (contact-box, required)",
  "seo": "component (shared.seo, required)"
}
```

**Searchable Attributes:** None specified
**Filterable Attributes:** None specified
**Sortable Attributes:** None specified

---

### 6. Contact (`contact`)

**Description:** Contact information and form page

**Schema:**
```json
{
  "title": "string (required, default: 'Contact')",
  "description": "text (required)",
  "contact": "component (contact-box, required)",
  "content": "richtext (optional)",
  "seo": "component (shared.seo, required)"
}
```

**Searchable Attributes:** None specified
**Filterable Attributes:** None specified
**Sortable Attributes:** None specified

---

### 7. Philosophy (`philosophy`)

**Description:** Company philosophy and values page

**Schema:**
```json
{
  "title": "string (required, default: 'Philosophy')",
  "description": "text (required)",
  "content": "richtext (required)",
  "seo": "component (shared.seo, required)"
}
```

**Searchable Attributes:** None specified
**Filterable Attributes:** None specified
**Sortable Attributes:** None specified

---

### 8. Vacancies (`vacancies`)

**Description:** Job openings and career opportunities

**Schema:**
```json
{
  "title": "string (required, default: 'Vacancies')",
  "description": "text (required)",
  "content": "richtext (required)",
  "seo": "component (shared.seo, required)"
}
```

**Searchable Attributes:** None specified
**Filterable Attributes:** None specified
**Sortable Attributes:** None specified

---

### 9. The Method (`the-method`)

**Description:** Methodology and approach explanation

**Schema:**
```json
{
  "title": "string (required, default: 'The Method')",
  "description": "text (required)",
  "content": "richtext (required)",
  "seo": "component (shared.seo, required)"
}
```

**Searchable Attributes:** None specified
**Filterable Attributes:** None specified
**Sortable Attributes:** None specified

---

### 10. Affiliates (`affiliates`)

**Description:** Affiliate program information

**Schema:**
```json
{
  "title": "string (required, default: 'Affiliates')",
  "description": "text (required)",
  "content": "richtext (required)",
  "seo": "component (shared.seo, required)"
}
```

**Searchable Attributes:** None specified
**Filterable Attributes:** None specified
**Sortable Attributes:** None specified

---

### 11. About (`about`)

**Description:** General about page

**Schema:**
```json
{
  "title": "string (required, default: 'About')",
  "description": "text (required)",
  "content": "richtext (required)",
  "seo": "component (shared.seo, required)"
}
```

**Searchable Attributes:** None specified
**Filterable Attributes:** None specified
**Sortable Attributes:** None specified

---

### 12. Practice Landing (`practice-landing`)

**Description:** Practice section landing page with clubs and audio journeys

**Schema:**
```json
{
  "headerPublic": "component (simple-header-sub, required)",
  "header": "component (simple-header-sub, required)",
  "headerLinks": "component[] (anchor-links-list, required, 2-6 items)",
  "featuredClubsHeader": "component (promo-section, required)",
  "featuredClubsList": "component[] (ft-event-session, required, 4-10 items)",
  "audioJourneysHeader": "component (simple-header-sub, required)",
  "featuredAudioJourneysHeader": "component (promo-section, required)",
  "featuredAudioJourneysList": "component[] (ft-event-series, required, 4-10 items)",
  "seo": "component (shared.seo, optional)"
}
```

**Searchable Attributes:** None specified
**Filterable Attributes:** None specified
**Sortable Attributes:** None specified

## Common Components

### Shared SEO Component (`shared.seo`)
```json
{
  "metaTitle": "string (required, max: 60 chars)",
  "metaDescription": "string (required, 50-160 chars)",
  "metaImage": "media (required)",
  "metaSocial": "component[] (meta-social)",
  "keywords": "text (optional)",
  "metaRobots": "string (optional)",
  "structuredData": "json (optional)",
  "metaViewport": "string (optional)",
  "canonicalURL": "string (optional)"
}
```

### Speaker List Component (`media.speaker-list`)
```json
{
  "teachers": "relation (oneToMany to entity)"
}
```

### Curriculum Component (`sessions.curriculum`)
```json
{
  "title": "string (required, default: 'What You'll Learn')",
  "KeyTakeaways": "component[] (curriculum-element, required)"
}
```

### File Element Component (`session-meta.file-element`)
```json
{
  "title": "string (required)",
  "description": "text (optional)",
  "attachment": "media (required)"
}
```

### Hyperlink Element Component (`session-meta.hyperlink-element`)
```json
{
  "title": "string (required)",
  "description": "text (optional)",
  "url": "string (required)"
}
```

### Text Element Component (`session-meta.text-element`)
```json
{
  "title": "string (required)",
  "description": "text (optional)",
  "content": "richtext (required)"
}
```

## Field Type Descriptions

- **string**: Short text field (typically up to 255 characters)
- **text**: Longer text field (multi-line, no formatting)
- **richtext**: Rich text editor with formatting capabilities
- **datetime**: Date and time picker
- **date**: Date picker only
- **decimal**: Decimal number field
- **integer**: Whole number field
- **boolean**: True/false field
- **uid**: Unique identifier, auto-generated from target field
- **media**: File upload field (images, videos, documents)
- **relation**: Reference to other content types
- **component**: Reusable structured content block
- **dynamiczone**: Flexible content area with multiple component types
- **json**: JSON data field
- **enumeration**: Predefined list of options

## Notes

1. All content types with `draftAndPublish: true` support draft/published states
2. Timestamps are automatically converted to Unix timestamps for filtering
3. Related content is populated through dynamic zones and relations
4. SEO components are standardized across all content types
5. Teacher/speaker lists are consistently implemented across content types
6. Media fields support multiple formats (images, videos, files) as specified
7. Pricing fields use decimal type for accurate financial calculations
8. Slug fields are auto-generated from title fields for SEO-friendly URLs
