const { formatTeacherData } = require("./helpers");


module.exports = {
  async beforeCreate(event) {
    if (!event.params.data.cardTitle && event.params.data.name) {
      event.params.data.cardTitle = event.params.data.name;
    }

    if (Boolean(Number(process.env.TEACHERS_USER_ACCOUNTS)) && !event.params.data.user_id) {
      const { user_id, user_email} = await strapi.service('api::entity.entity')
        .createTeacherUserAccount(await formatTeacherData(event.params.data));
      event.params.data.user_id = new String(user_id)
      event.params.data.user_email = new String(user_email)
    }
  },
  async beforeUpdate(event) {
    // Fetch the entity for both cardTitle and user account logic
    const entity = await strapi.entityService.findOne('api::entity.entity', event.params.where.id, {
      populate: {
        picture: {populate: {formats: {populate: {thumbnail: true}}}},
        cover: {populate: {formats: {populate: {thumbnail: true}}}},
        user_id: true,
      }
    })

    if (!event.params.data.cardTitle && (event.params.data.name || entity.name)) {
      event.params.data.cardTitle = event.params.data.name || entity.name;
    }

    if (Boolean(Number(process.env.TEACHERS_USER_ACCOUNTS))) {
      if (!event.params.data.user_id) {
        const {user_id, user_email} = await strapi.service('api::entity.entity')
          .createTeacherUserAccount(await formatTeacherData({
            ...event.params.data,
            slug: entity.slug,
            name: entity.name,
            description: entity.description,
            user_email: entity.user_email,
            picture: typeof event.params.data.picture === "number" ? entity.picture : event.params.data.picture,
          }));
        event.params.data.user_id = new String(user_id)
        event.params.data.user_email = new String(user_email)
      }
    }
  },
  async afterUpdate(event) {
    // Skip cache invalidation during bulk updates to prevent cascading
    const collectionsService = strapi.plugin('update-collections-trigger').service('collections');
    if (!collectionsService.isBulkUpdateInProgress()) {
      collectionsService.invalidateCache('entities', event.result.slug);
    }
  },
};
