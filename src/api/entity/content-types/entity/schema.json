{"kind": "collectionType", "collectionName": "entities", "info": {"singularName": "entity", "pluralName": "entities", "displayName": "Faculty | Teachers", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "cardTitle": {"type": "string", "required": false}, "description": {"type": "text", "required": true}, "picture": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "cover": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "bio": {"type": "richtext", "required": true}, "Related": {"type": "dynamiczone", "components": ["media.related-course", "media.related-event", "media.related-article", "media.related-film", "media.related-podcast", "media.related-webinar"], "required": false, "max": 5, "min": 1}, "slug": {"type": "uid", "targetField": "name", "required": true}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo", "required": false}, "ranking": {"type": "integer", "min": 0, "max": 1000, "required": true, "default": 0}, "user_id": {"type": "string"}, "user_email": {"type": "string", "regex": "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$"}, "relatedCourses": {"type": "relation", "relation": "oneToMany", "target": "api::course.course"}, "relatedEvents": {"type": "relation", "relation": "oneToMany", "target": "api::event.event"}, "relatedArticles": {"type": "relation", "relation": "oneToMany", "target": "api::read-entry.read-entry"}, "relatedFilms": {"type": "relation", "relation": "oneToMany", "target": "api::watch-entry.watch-entry"}, "relatedPodcasts": {"type": "relation", "relation": "oneToMany", "target": "api::listen-entry.listen-entry"}, "relatedWebinars": {"type": "relation", "relation": "oneToMany", "target": "api::webinar.webinar"}}}