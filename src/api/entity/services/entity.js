'use strict';
const axios = require('axios')

/**
 * entity service.
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::entity.entity', ({ strapi }) =>  ({
  async createTeacherUserAccount(teacherData) {
    try {
      const apiBase = process.env.API_URL
      const magicKey = process.env.API_MAGIC_KEY
      const { data } = await axios.post(
        `${apiBase}/dispatcher/create_teacher_user_account?magicKey=${magicKey}`,
        { teacherData })
      console.log(`Teacher [${teacherData.slug}] user account [${data.user_email}] created successfully`)

      return data
    } catch (error) {
      console.log('create-teacher-user-account:', error)
      strapi
        .plugin('sentry')
        .service('sentry')
        .sendError(error);
      return {
        user_id: '',
        user_email: '',
      }
    }
  },
}));
