'use strict';
const { v4: uuid } = require('uuid');
const axios = require('axios')


/**
 * course service.
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::course.course', ({ strapi }) =>  ({
  async getHubspotListId(name) {
    // TODO: move service to plugin
    if (!Boolean(Number(process.env.HUBSPOT_ENABLED))) {
      console.log("services:course:getHubspotListId:disabled")
      return ''
    } else {
      try {
        const headers = { 'Authorization': `Bearer ${process.env.HUBSPOT_ACCESS_TOKEN}` }
        const { data } = await axios.get(
          `https://api.hubapi.com/crm/v3/lists/object-type-id/0-1/name/${name}`, { headers })
        if (data.list?.listId) {
          console.log(`getHubspotListId: found list "${name}" with ILS id ${data.list.listId}`)
          return data.list.listId
        } else {
          console.log(`getHubspotListId: list "${name}" not found`)
          return ''
        }
      } catch (error) {
        console.log(error)
        console.log('getHubspotListId:', error.response?.data?.message)
        strapi
          .plugin('sentry')
          .service('sentry')
          .sendError(error);
        return ''
      }
    }
  },
  async createHubSpotList(name) {
    // TODO: move service to plugin
    if (!Boolean(Number(process.env.HUBSPOT_ENABLED))) {
      console.log("services:course:createHubSpotList:disabled")
      return ''
    } else {
      try {
        const headers = { 'Authorization': `Bearer ${process.env.HUBSPOT_ACCESS_TOKEN}` }
        const payload = {
          name,
          objectTypeId: "0-1",
          processingType: "MANUAL"
        }
        const { data } = await axios.post('https://api.hubapi.com/crm/v3/lists', payload, { headers })
        console.log(`createHubSpotList: created list ${name} with ILS id ${data.list.listId}`)

        return data.list.listId;
      } catch (error) {
        if (error.message.includes("already exist") || error.response?.data?.message.includes("already exist")) {
          console.log(`createHubSpotList: list "${name}" already exists, getting ILS id`)
          return await this.getHubspotListId(name)
        } else {
          console.log('createHubSpotList:', error.message)
          strapi
            .plugin('sentry')
            .service('sentry')
            .sendError(error);
          return ''
        }
      }
    }
  },
  async createDiscussion(contentType, contentSlug, contentTitle) {
    // TODO: move service to plugin
    let discussionId = ''
    if (!Boolean(Number(process.env.COMMUNITY_ENABLED))) {
      console.log("services:course:createDiscussion:disabled")
      return discussionId
    } else {
      try {
        const apiBase = process.env.API_URL
        const magicKey = process.env.API_MAGIC_KEY

        if (!apiBase || !magicKey) {
          console.warn(`services:course:createDiscussion: Missing API_URL or API_MAGIC_KEY environment variables`)
          return discussionId
        }

        try {
          const { data } = await axios.post(
            `${apiBase}/dispatcher/create_discussion_thread?magicKey=${magicKey}`, {
              contentType, contentSlug, contentTitle
            })
          discussionId = data?.discussionId
          console.log(`services:course:createDiscussion: ${contentType}/${contentSlug} created with id ${discussionId}`)
        } catch(error) {
          console.error(`services:course:createDiscussion failed for ${contentType}/${contentSlug}:`, error.message)
        }

        return discussionId
      } catch (error) {
        console.error(`services:course:createDiscussion error for ${contentType}/${contentSlug}:`, error)
        strapi
          .plugin('sentry')
          .service('sentry')
          .sendError(error);
        return discussionId
      }
    }
  },
}));
