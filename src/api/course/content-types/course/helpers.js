const canCreateCourseDiscussion = async (data, course = null) => {
  if (course === null) course = await strapi.entityService.findOne(
      "api::course.course", data.params.where.id)
  // if it's being published and the course doesn't already have a discussionId
  if ("publishedAt" in data.params.data)
    return data.params.data.publishedAt !== null && course.discussionId === null
  // if it's removing the discussionId and it's published (recreate the category?)
  if ("discussionId" in data.params.data)
    return data.params.data.discussionId === null && course.publishedAt !== null
  // if the course doesn't already have a discussionId and it's not being assigned one and it's published
  return course.discussionId === null &&
    !("discussionId" in data.params.data) &&
      course.publishedAt !== null
}

const extractTeachersCommunityUserId = async (course) => {
  let uids = []
  for (let module of course.modules)
    for (let session of module.sessions){
      for (let teacher of session.teachersList?.teachers)
        uids.push(teacher.community_user_id)
    }
  // remove list duplicates
  uids = uids
    .filter((value, index, self) => self.indexOf(value) === index)
    .filter(uid => uid !== "null")
    .filter(Boolean)
  return uids
}

module.exports = {
  canCreateCourseDiscussion,
  extractTeachersCommunityUserId,
}
