const slugify = require("slugify");
const { v4: uuid } = require('uuid');

const { ApplicationError } = require('@strapi/utils').errors;

const {
  canCreateCourseDiscussion,
} = require('./helpers');
const {
  formatDataForChargebeeChargeItem,
  generateChargebeeEntityId,
} = require('../../../../utils/chargebee');

const {
  getRelatedContent,
  isContentAlreadyRelated,
  updateSessionsWithContentId,
  updateRelatedContent } = require('../../../../utils/content');



module.exports = {
  async beforeCreate(event) {
    if (!event.params.data.cardTitle && event.params.data.title) {
      event.params.data.cardTitle = event.params.data.title;
    }

    event.params.data.chargebee_entity_id = await generateChargebeeEntityId('courses');
    if (!event.params.data.slug) {
      event.params.data.slug = slugify(
          event.params.data.title, {lower: true});
    }
    if (!event.params.data.hubspot_list_id) {
      event.params.data.hubspot_list_id = await strapi.service('api::course.course')
        .createHubSpotList(`${event.params.data.title} [course]`);
    }
    if (!event.params.data.discussionId && event.params.data.title && event.params.data.slug) {
      event.params.data.discussionId = await strapi.service('api::course.course').createDiscussion(
        'courses', event.params.data.slug, event.params.data.title)
    }
  },
  async afterCreate(event) {
    const data = await formatDataForChargebeeChargeItem(event.result, event.result, 'courses');
    await strapi.service(
        'api::subscription-plan.subscription-plan').createChargebeeChargeItem(data);
  },
  async beforeUpdate(event) {
    const courseEntry = await strapi.entityService.findOne(
        'api::course.course', event.params.where.id, {
          populate: { modules: { populate: { sessions: {
            populate: { teachersList: { populate: { teachers: true } } }
          } } } }
        })


    if (!event.params.data.cardTitle && (event.params.data.title || courseEntry.title)) {
      event.params.data.cardTitle = event.params.data.title || courseEntry.title;
    }

    if (!event.params.data.chargebee_entity_id) {
      event.params.data.chargebee_entity_id = await generateChargebeeEntityId('courses');
      const data = await formatDataForChargebeeChargeItem(event.params.data, courseEntry, 'courses');
      await strapi.service(
          'api::subscription-plan.subscription-plan').createChargebeeChargeItem(data);
    }
    if (event.params.data.price &&
        courseEntry.price !== event.params.data.price) {
      // update Chargebee item price
      await strapi.service('api::subscription-plan.subscription-plan')
        .updateChargebeeChargeItem({
          entityId: courseEntry.chargebee_entity_id,
          price: parseInt(event.params.data.price * 100)
        });
    }
    if (event.params.data.slug &&
        courseEntry.slug !== event.params.data.slug) {
      // update API users' content with new slug
      await strapi.plugin('deployment-trigger').service('deployment')
        .updateSlug('courses', courseEntry.slug, event.params.data.slug);
    }
    if (!courseEntry.hubspot_list_id && !event.params.data.hubspot_list_id) {
      const title = event.params.data.title || courseEntry.title
      event.params.data.hubspot_list_id = await strapi.service('api::course.course')
        .createHubSpotList(`${title} [course]`);
    }
    if (!(event.params.data.discussionId || courseEntry.discussionId) &&
        (event.params.data.title || courseEntry.title) &&
        (event.params.data.slug || courseEntry.slug)) {
      event.params.data.discussionId = await strapi.service('api::course.course').createDiscussion(
        'courses',
        event.params.data.slug || courseEntry.slug,
        event.params.data.title || courseEntry.title)
    }
  },
  async afterUpdate(event) {
    // updates the course in each course session so later on when a session is
    // updated, its course also updates the components' data.
    const courseEntry = await strapi.entityService.findOne(
        'api::course.course', event.result.id, {
          populate: {
            modules: { populate: { sessions: true } },
            liveConversations: { populate: { sessions: true } },
          }
        })
    await updateSessionsWithContentId(courseEntry, 'course', true)

    if (event.result?.Related) {
      // add bidirectional relationship in the results in the Related field
      for (let related of event.result.Related) {
        const relatedContentType = `${related.__component.replace('media.related-', '')}s`
        const relatedContent = await getRelatedContent(strapi, related, relatedContentType)

        // if the event.result.id already exists in relatedFilm.Related, skip
        const alreadyRelated = isContentAlreadyRelated(
          relatedContent, 'course', event.result.id)

        // update every related course Related field with the current course (event.result)
        if (relatedContent.id && (!relatedContent.Related || !alreadyRelated) ) {
          await updateRelatedContent(
            strapi, relatedContent, relatedContentType, event.result.id, 'course')
        }
      }
    }
    // Skip updatePages() and cache invalidation during bulk updates to prevent cascading
    const collectionsService = strapi.plugin('update-collections-trigger').service('collections');
    if (!collectionsService.isBulkUpdateInProgress()) {
      await strapi.plugin('deployment-trigger').service('deployment').updatePages();
      collectionsService.invalidateCache('courses', event.result.slug || courseEntry.slug);
    }
  },
};
