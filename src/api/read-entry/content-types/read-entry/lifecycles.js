const slugify = require("slugify");
const { v4: uuid } = require('uuid');

const {
  getRelatedContent,
  isContentAlreadyRelated,
  updateRelatedContent } = require('../../../../utils/content');

module.exports = {
  async beforeCreate(event) {
    if (!event.params.data.cardTitle && event.params.data.title) {
      event.params.data.cardTitle = event.params.data.title;
    }

    const entity_id = uuid();
    event.params.data.chargebee_entity_id = `articles_${entity_id}`;
    if (!event.params.data.slug) {
      event.params.data.slug = slugify(
          event.params.data.title, { lower: true });
    }
  },
  async afterCreate(event) {
    // const data = {
    //   id: event.result.id,
    //   itemId: `articles_${event.result.id}-${event.result.slug}`,
    //   itemName: `${event.result.title} [article]`,
    //   entityId: event.result.chargebee_entity_id,
    //   price: parseInt(event.result.price * 100),
    // }
    // await strapi.service(
    //     'api::subscription-plan.subscription-plan').createChargebeeChargeItem(data);
  },
  async beforeUpdate(event) {
    const readEntry = await strapi.entityService.findOne(
        'api::read-entry.read-entry', event.params.where.id)


    if (!event.params.data.cardTitle && (event.params.data.title || readEntry.title)) {
      event.params.data.cardTitle = event.params.data.title || readEntry.title;
    }

    // if (event.params.data.price &&
    //     readEntry.price !== event.params.data.price) {
    //   // update Chargebee item price
    //   await strapi.service('api::subscription-plan.subscription-plan')
    //     .updateChargebeeChargeItem({
    //       entityId: listenEntry.chargebee_entity_id,
    //       price: parseInt(event.params.data.price * 100)
    //     });
    // }
    if (event.params.data.slug &&
        readEntry.slug !== event.params.data.slug) {
      // update API users' content with new slug
      await strapi.plugin('deployment-trigger').service('deployment')
        .updateSlug('articles', readEntry.slug, event.params.data.slug);
    }
  },
  async afterUpdate(event) {
    // TODO: check if teacher was removed and update it as well
    // update entities related content
    if (event.result?.TeacherList?.teachers) {
      for (let t of event.result.TeacherList.teachers) {
        const teacher = await strapi.entityService.findOne('api::entity.entity', t.id, {
          populate: { relatedArticles: true }
        })
        await strapi.entityService.update('api::entity.entity', t.id, {
          data: {
            relatedArticles: [...teacher.relatedArticles, event.params.where.id]
          }
        });
      }
    }

    if (event.result?.Related) {
      // add bidirectional relationship in the results in the Related field
      for (let related of event.result.Related) {
        const relatedContentType = `${related.__component.replace('media.related-', '')}s`
        const relatedContent = await getRelatedContent(strapi, related, relatedContentType)

        // if the event.result.id already exists in relatedFilm.Related, skip
        const alreadyRelated = isContentAlreadyRelated(
          relatedContent, 'article', event.result.id)

        // update every related article Related field with the current article (event.result)
        if (relatedContent.id && (!relatedContent.Related || !alreadyRelated) ) {
          await updateRelatedContent(
            strapi, relatedContent, relatedContentType, event.result.id, 'article')
        }
      }
    }
    // Skip updatePages() and cache invalidation during bulk updates to prevent cascading
    const collectionsService = strapi.plugin('update-collections-trigger').service('collections');
    if (!collectionsService.isBulkUpdateInProgress()) {
      await strapi.plugin('deployment-trigger').service('deployment').updatePages();
      collectionsService.invalidateCache('articles', event.result.slug);
    }
  },
};
