const canCreateEventSessionDiscussion = async (session, event = null) => {
  // if the event exists or the session has an event associated
  // const eventId = eventData.params.data.event?.id || eventData.params.data.event?.connect[0]?.id
  const eventId = session.course?.id
  if (event === null && !!eventId)
    event = await strapi.entityService.findOne("api::event.event", eventId)
  else if (!event && !eventId) return false

  // if doesn't have already a category, the event is published and has a discussionId
  return (!!session && !session?.discussionId &&
  // return (!eventData.params?.data?.discussionId &&
          // eventData.discussionId) &&  // in case event is a courseSession
      !!event.publishedAt && !!event.discussionId)
}

module.exports = {
  canCreateEventSessionDiscussion,
}
