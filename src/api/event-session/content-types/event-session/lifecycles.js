module.exports = {
  async beforeCreate(event) {
    if (!event.params.data.cardTitle && event.params.data.title) {
      event.params.data.cardTitle = event.params.data.title;
    }
  },
  async beforeUpdate(event) {
    const eventSession = await strapi.entityService.findOne(
        'api::event-session.event-session', event.params.where.id)

    if (!event.params.data.cardTitle && (event.params.data.title || eventSession.title)) {
      event.params.data.cardTitle = event.params.data.title || eventSession.title;
    }
  },
  async afterUpdate(event) {
    const session = await strapi.entityService.findOne(
        'api::event-session.event-session', event.result.id, {
          populate: { event: true }
        })
    // updates the event related to the event session
    // if (!!session.event && !!session.event.id) {
    //   await strapi.entityService.update('api::event.event', session.event.id, {
    //     data: {
    //       id: session.event.id,
    //     },
    //   });
    // }
    // TODO: check if teacher was removed and update it as well
    if (event.result?.teachersList?.teachers) {
      // update entities related content
      for (let t of event.result.teachersList.teachers) {
        const teacher = await strapi.entityService.findOne('api::entity.entity', t.id, {
          populate: { relatedEvents: true }
        })
        await strapi.entityService.update('api::entity.entity', t.id, { data: {
            relatedEvents: [...teacher.relatedEvents, session.event.id]
          } });
      }
    }
    // Skip updatePages() and cache invalidation during bulk updates to prevent cascading
    const collectionsService = strapi.plugin('update-collections-trigger').service('collections');
    if (!collectionsService.isBulkUpdateInProgress()) {
      await strapi.plugin('deployment-trigger').service('deployment').updatePages();
      collectionsService.invalidateCache('event-sessions', session.event?.slug, event.result.slug || session.slug);
    }
  }
};
