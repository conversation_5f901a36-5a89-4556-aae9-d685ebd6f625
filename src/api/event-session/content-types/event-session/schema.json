{"kind": "collectionType", "collectionName": "event_sessions", "info": {"singularName": "event-session", "pluralName": "event-sessions", "displayName": "Practice | Journeys & Club sessions", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "cardTitle": {"type": "string", "required": false}, "description": {"type": "text", "required": true}, "about": {"type": "richtext"}, "liveURL": {"type": "string"}, "dateHeadline": {"type": "string"}, "headline": {"type": "string", "required": false}, "startDateTime": {"type": "datetime", "required": true}, "endDateTime": {"type": "datetime"}, "event": {"type": "relation", "relation": "oneToOne", "target": "api::event.event"}, "slug": {"type": "uid", "targetField": "title", "required": true}, "videoURL": {"type": "relation", "relation": "oneToOne", "target": "plugin::mux-video-uploader.mux-asset"}, "videoThumbnail": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "videoTimecode": {"type": "integer", "min": 0, "required": false}, "audioURL": {"type": "relation", "relation": "oneToOne", "target": "plugin::mux-video-uploader.mux-asset"}, "transcript": {"allowedTypes": ["files"], "type": "media", "multiple": false, "required": false}, "thumbnail": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "picture": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "teachersList": {"type": "component", "repeatable": false, "component": "session-meta.speaker-list", "required": true}, "readingResources": {"type": "dynamiczone", "components": ["session-meta.file-element", "session-meta.hyperlink-element", "session-meta.text-element"], "required": false}, "practices": {"type": "dynamiczone", "components": ["session-meta.file-element", "session-meta.hyperlink-element", "session-meta.text-element"], "required": false}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo", "required": false}, "moduleTitle": {"type": "string"}, "moduleIndex": {"type": "integer", "default": 0}, "chapterIndexInModule": {"type": "integer", "default": 0}}}