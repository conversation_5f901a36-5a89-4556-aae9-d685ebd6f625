const slugify = require("slugify");
const { v4: uuid } = require('uuid');
const { createLivestream } = require('../../../../utils/mux');

const { ApplicationError } = require('@strapi/utils').errors;

const {
  getRelatedContent,
  isContentAlreadyRelated,
  updateRelatedContent } = require('../../../../utils/content');

const {
  formatDataForChargebeeChargeItem,
  generateChargebeeEntityId,
} = require('../../../../utils/chargebee');


module.exports = {
  async beforeCreate(event) {
    if (!event.params.data.cardTitle && event.params.data.title) {
      event.params.data.cardTitle = event.params.data.title;
    }

    event.params.data.chargebee_entity_id = await generateChargebeeEntityId('webinars');
    if (!event.params.data.slug) {
      event.params.data.slug = slugify(
          event.params.data.title, {lower: true});
    }
    // event.params.data.slug = slugify(event.params.data.title);
    if (!event.params.data.hubspot_list_id) {
      event.params.data.hubspot_list_id = await strapi.service('api::course.course')
        .createHubSpotList(`${event.params.data.title} [webinar]`);
    }
    if (event.params.data.zoomMeetingNumber) {
      event.params.data.zoomMeetingNumber = event.params.data.zoomMeetingNumber.replaceAll(" ", "")
      event.params.data.zoomMeetingPassword = event.params.data.zoomMeetingPassword.replaceAll(" ", "")
    }
    if (!event.params.data.discussionId && event.params.data.title && event.params.data.slug) {
      event.params.data.discussionId = await strapi.service('api::course.course').createDiscussion(
        'webinars', event.params.data.slug, event.params.data.title)
    }
  },
  async afterCreate(event) {
    // const data = await formatDataForChargebeeChargeItem(event.result, event.result, 'webinars');
    // await strapi.service(
    //     'api::subscription-plan.subscription-plan').createChargebeeChargeItem(data);
  },
  async beforeUpdate(event) {
    const webinarEntry = await strapi.entityService.findOne(
        'api::webinar.webinar', event.params.where.id, {})


    if (!event.params.data.cardTitle && (event.params.data.title || webinarEntry.title)) {
      event.params.data.cardTitle = event.params.data.title || webinarEntry.title;
    }

    // if (!event.params.data.chargebee_entity_id) {
    //   event.params.data.chargebee_entity_id = await generateChargebeeEntityId('webinars');
    //   const data = await formatDataForChargebeeChargeItem(event.params.data, webinarEntry, 'webinars');
    //   await strapi.service(
    //       'api::subscription-plan.subscription-plan').createChargebeeChargeItem(data);
    // }
    if (event.params.data.price &&
        webinarEntry.price !== event.params.data.price) {
      // update Chargebee item price
      await strapi.service('api::subscription-plan.subscription-plan')
        .updateChargebeeChargeItem({
          entityId: webinarEntry.chargebee_entity_id,
          price: parseInt(event.params.data.price * 100)
        });
    }
    if (event.params.data.slug &&
        webinarEntry.slug !== event.params.data.slug) {
      // update API users' content with new slug
      await strapi.plugin('deployment-trigger').service('deployment')
        .updateSlug('webinars', webinarEntry.slug, event.params.data.slug);
    }
    if (!webinarEntry.hubspot_list_id && !event.params.data.hubspot_list_id) {
      const title = event.params.data.title || webinarEntry.title
      event.params.data.hubspot_list_id = await strapi.service('api::course.course')
        .createHubSpotList(`${title} [webinar]`);
    }
    if (event.params.data.zoomMeetingNumber) {
      event.params.data.zoomMeetingNumber = event.params.data.zoomMeetingNumber.replaceAll(" ", "")
      event.params.data.zoomMeetingPassword = event.params.data.zoomMeetingPassword.replaceAll(" ", "")
    }
    if (!(event.params.data.discussionId || webinarEntry.discussionId) &&
        (event.params.data.title || webinarEntry.title) &&
        (event.params.data.slug || webinarEntry.slug)) {
      event.params.data.discussionId = await strapi.service('api::course.course').createDiscussion(
        'courses',
        event.params.data.slug || webinarEntry.slug,
        event.params.data.title || webinarEntry.title)
    }
  },
  async afterUpdate(event) {
    // TODO: check if teacher was removed and update it as well
    if (event.result?.TeacherList?.teachers) {
      // update entities related content
      for (let t of event.result.TeacherList.teachers) {
        const teacher = await strapi.entityService.findOne('api::entity.entity', t.id, {
          populate: { relatedWebinars: true }
        })
        await strapi.entityService.update('api::entity.entity', t.id, {
          data: {
            relatedWebinars: [...teacher.relatedWebinars, event.params.where.id]
          }
        });
      }
    }

    if (event.result?.Related) {
      // add bidirectional relationship in the results in the Related field
      for (let related of event.result.Related) {
        const relatedContentType = `${related.__component.replace('media.related-', '')}s`
        const relatedContent = await getRelatedContent(strapi, related, relatedContentType)

        // if the event.result.id already exists in relatedFilm.Related, skip
        const alreadyRelated = isContentAlreadyRelated(
          relatedContent, 'podcast', event.result.id)

        // update every related webinar Related field with the current webinar (event.result)
        if (relatedContent.id && (!relatedContent.Related || !alreadyRelated) ) {
          await updateRelatedContent(
            strapi, relatedContent, relatedContentType, event.result.id, 'webinar')
        }
      }
    }
    // Skip updatePages() and cache invalidation during bulk updates to prevent cascading
    const collectionsService = strapi.plugin('update-collections-trigger').service('collections');
    if (!collectionsService.isBulkUpdateInProgress()) {
      await strapi.plugin('deployment-trigger').service('deployment').updatePages();
      collectionsService.invalidateCache('webinars', event.result.slug || webinarEntry.slug);
    }
  }
};
