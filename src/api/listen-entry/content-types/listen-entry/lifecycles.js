const slugify = require("slugify");
const { v4: uuid } = require('uuid');

const {
  getRelatedContent,
  isContentAlreadyRelated,
  updateRelatedContent } = require('../../../../utils/content');

module.exports = {
  async beforeCreate(event) {
    if (!event.params.data.cardTitle && event.params.data.title) {
      event.params.data.cardTitle = event.params.data.title;
    }

    const entity_id = uuid();
    event.params.data.chargebee_entity_id = `podcasts_${entity_id}`;
    if (!event.params.data.slug) {
      event.params.data.slug = slugify(
          event.params.data.title, { lower: true });
    }
  },
  async afterCreate(event) {
    // const data = {
    //   id: event.result.id,
    //   itemId: `podcasts_${event.result.id}-${event.result.slug}`,
    //   itemName: `${event.result.title} [podcast]`,
    //   entityId: event.result.chargebee_entity_id,
    //   price: parseInt(event.result.price * 100),
    // }
    // await strapi.service(
    //     'api::subscription-plan.subscription-plan').createChargebeeChargeItem(data);
  },
  async beforeUpdate(event) {
    const listenEntry = await strapi.entityService.findOne(
        'api::listen-entry.listen-entry', event.params.where.id)


    if (!event.params.data.cardTitle && (event.params.data.title || listenEntry.title)) {
      event.params.data.cardTitle = event.params.data.title || listenEntry.title;
    }

    // if (event.params.data.price &&
    //     listenEntry.price !== event.params.data.price) {
    //   // update Chargebee item price
    //   await strapi.service('api::subscription-plan.subscription-plan')
    //     .updateChargebeeChargeItem({
    //       entityId: listenEntry.chargebee_entity_id,
    //       price: parseInt(event.params.data.price * 100)
    //     });
    // }
    if (event.params.data.slug &&
        listenEntry.slug !== event.params.data.slug) {
      // update API users' content with new slug
      await strapi.plugin('deployment-trigger').service('deployment')
        .updateSlug('podcasts', listenEntry.slug, event.params.data.slug);
    }
  },
  async afterUpdate(event) {
    // TODO: check if teacher was removed and update it as well
    if (event.result?.TeacherList?.teachers) {
      // update entities related content
      for (let t of event.result.TeacherList.teachers) {
        const teacher = await strapi.entityService.findOne('api::entity.entity', t.id, {
          populate: { relatedPodcasts: true }
        })
        await strapi.entityService.update('api::entity.entity', t.id, {
          data: {
            relatedPodcasts: [...teacher.relatedPodcasts, event.params.where.id]
          }
        });
      }
    }

    if (event.result?.Related) {
      // add bidirectional relationship in the results in the Related field
      for (let related of event.result.Related) {
        const relatedContentType = `${related.__component.replace('media.related-', '')}s`
        const relatedContent = await getRelatedContent(strapi, related, relatedContentType)

        // if the event.result.id already exists in relatedFilm.Related, skip
        const alreadyRelated = isContentAlreadyRelated(
          relatedContent, 'podcast', event.result.id)

        // update every related podcast Related field with the current podcast (event.result)
        if (relatedContent.id && (!relatedContent.Related || !alreadyRelated) ) {
          await updateRelatedContent(
            strapi, relatedContent, relatedContentType, event.result.id, 'podcast')
        }
      }
    }
    // Skip updatePages() and cache invalidation during bulk updates to prevent cascading
    const collectionsService = strapi.plugin('update-collections-trigger').service('collections');
    if (!collectionsService.isBulkUpdateInProgress()) {
      await strapi.plugin('deployment-trigger').service('deployment').updatePages();
      collectionsService.invalidateCache('podcasts', event.result.slug);
    }
  },
};
