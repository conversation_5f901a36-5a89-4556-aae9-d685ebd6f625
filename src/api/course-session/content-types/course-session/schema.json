{"kind": "collectionType", "collectionName": "course_sessions", "info": {"singularName": "course-session", "pluralName": "course-sessions", "displayName": "Learn | Courses' Chapters", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "cardTitle": {"type": "string", "required": false}, "description": {"type": "text", "required": true}, "about": {"type": "richtext"}, "liveURL": {"type": "string"}, "startDateTime": {"type": "datetime"}, "endDateTime": {"type": "datetime"}, "course": {"type": "relation", "relation": "oneToOne", "target": "api::course.course"}, "slug": {"type": "uid", "targetField": "title", "required": true}, "videoURL": {"type": "relation", "relation": "oneToOne", "target": "plugin::mux-video-uploader.mux-asset"}, "videoThumbnail": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "videoTimecode": {"type": "integer", "min": 0, "required": false}, "q-and-a": {"type": "boolean", "default": false}, "audioURL": {"type": "relation", "relation": "oneToOne", "target": "plugin::mux-video-uploader.mux-asset"}, "transcript": {"allowedTypes": ["files"], "type": "media", "multiple": false, "required": false}, "thumbnail": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "picture": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "teachersList": {"type": "component", "repeatable": false, "component": "session-meta.speaker-list", "required": true}, "readingResources": {"type": "dynamiczone", "components": ["session-meta.file-element", "session-meta.hyperlink-element", "session-meta.text-element"], "required": false}, "practices": {"type": "dynamiczone", "components": ["session-meta.file-element", "session-meta.hyperlink-element", "session-meta.text-element"], "required": false}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo", "required": false}, "moduleTitle": {"type": "string"}, "moduleIndex": {"type": "integer", "default": 0}, "chapterIndexInModule": {"type": "integer", "default": 0}}}