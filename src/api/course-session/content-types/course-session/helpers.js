const canCreateCourseSessionDiscussion = async (session, course = null) => {
  // if the course exists or the session has a course associated
  // const courseId = event.params.data.course?.id || event.params.data.course?.connect[0]?.id
  const courseId = session.course?.id
  if (course === null && !!courseId)
    course = await strapi.entityService.findOne("api::course.course", courseId)
  else if (!course && !courseId) return false

  // if doesn't have already a category, the course is published and has a community_category_id
  return (!!session && !session?.discussionId &&
  // return (!event.params?.data?.discussionId &&
          // data.discussionId) &&  // in case event is a courseSession
      !!course.publishedAt && !!course.discussionId)
}

module.exports = {
  canCreateCourseSessionDiscussion,
}
