const slugify = require("slugify");
const { v4: uuid } = require('uuid');

const { ApplicationError } = require('@strapi/utils').errors;

const {
  getRelatedContent,
  isContentAlreadyRelated,
  updateSessionsWithContentId,
  updateRelatedContent } = require('../../../../utils/content');
const {
  formatDataForChargebeeChargeItem,
  generateChargebeeEntityId,
} = require('../../../../utils/chargebee');


const updateEventSessionsWithEventId = async (eventEntry) => {
  if (eventEntry.modules && eventEntry.modules.sessions?.length !== 0) {
    eventEntry.modules.map(m => m.sessions.map(async session => {
      const eventSession = await strapi.entityService.findOne(
          'api::event-session.event-session', session.id, {
            populate: { course: true }
          })
      if (!eventSession.event)
        await strapi.entityService.update(
            'api::event-session.event-session', session.id, {
              data: { event: { id: eventEntry.id } }, });
    }))
  }
}


module.exports = {
  async beforeCreate(event) {
    if (!event.params.data.cardTitle && event.params.data.title) {
      event.params.data.cardTitle = event.params.data.title;
    }

    event.params.data.chargebee_entity_id = await generateChargebeeEntityId('clubs');
    if (!event.params.data.slug) {
      event.params.data.slug = slugify(
          event.params.data.title, {lower: true});
    }
    if (!event.params.data.hubspot_list_id) {
      event.params.data.hubspot_list_id = await strapi.service('api::course.course')
        .createHubSpotList(`${event.params.data.title} [club]`);
    }
    if (!event.params.data.discussionId && event.params.data.title && event.params.data.slug) {
      event.params.data.discussionId = await strapi.service('api::course.course').createDiscussion(
        event.params.data.recurring? 'clubs' : 'audio-journeys',
        event.params.data.slug,
        event.params.data.title)
    }
  },
  async afterCreate(event) {
    // const data = await formatDataForChargebeeChargeItem(event.result, event.result, 'clubs');
    // await strapi.service(
    //     'api::subscription-plan.subscription-plan').createChargebeeChargeItem(data);
  },
  async beforeUpdate(event) {
    const eventEntry = await strapi.entityService.findOne(
        'api::event.event', event.params.where.id, {
          populate: { modules: { populate: { sessions: true } } }
        })


    if (!event.params.data.cardTitle && (event.params.data.title || eventEntry.title)) {
      event.params.data.cardTitle = event.params.data.title || eventEntry.title;
    }
    if (!eventEntry.hubspot_list_id && !event.params.data.hubspot_list_id) {
      const title = event.params.data.title || eventEntry.title
      event.params.data.hubspot_list_id = await strapi.service('api::course.course')
        .createHubSpotList(`${title} [event]`);
    }
    const contentType = (eventEntry.recurring || event.params.data.recurring)?
      'clubs' : 'audio-journeys'
    if (event.params.data.slug &&
        eventEntry.slug !== event.params.data.slug) {
      // update API users' content with new slug
      await strapi.plugin('deployment-trigger').service('deployment')
        .updateSlug(contentType, eventEntry.slug, event.params.data.slug);
    }
    if (!(event.params.data.discussionId || eventEntry.discussionId) &&
        (event.params.data.title || eventEntry.title) &&
        (event.params.data.slug || eventEntry.slug)) {
      event.params.data.discussionId = await strapi.service('api::course.course').createDiscussion(
        contentType,
        event.params.data.slug || eventEntry.slug,
        event.params.data.title || eventEntry.title)
    }
  },
  async afterUpdate(event) {
    // updates the event in each event session so later on when a session is
    // updated, its event also updates the components' data.
    const eventEntry = await strapi.entityService.findOne(
        'api::event.event', event.result.id, {
          populate: { modules: { populate: { sessions: true } } }
        })
    await updateSessionsWithContentId(eventEntry, 'event')

    if (event.result?.Related) {
      // add bidirectional relationship in the results in the Related field
      for (let related of event.result.Related) {
        const relatedContentType = `${related.__component.replace('media.related-', '')}s`
        const relatedContent = await getRelatedContent(strapi, related, relatedContentType)

        // if the event.result.id already exists in relatedFilm.Related, skip
        const alreadyRelated = isContentAlreadyRelated(
          relatedContent, 'event', event.result.id)

        // update every related event Related field with the current event (event.result)
        if (relatedContent.id && (!relatedContent.Related || !alreadyRelated) ) {
          await updateRelatedContent(
            strapi, relatedContent, relatedContentType, event.result.id, 'event')
        }
      }
    }
    // Skip updatePages() and cache invalidation during bulk updates to prevent cascading
    const collectionsService = strapi.plugin('update-collections-trigger').service('collections');
    if (!collectionsService.isBulkUpdateInProgress()) {
      await strapi.plugin('deployment-trigger').service('deployment').updatePages();
      collectionsService.invalidateCache('event-sessions', event.result.slug || eventEntry.slug);
    }
  },
};
