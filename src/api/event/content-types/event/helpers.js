const canCreateEventDiscussion = async (data, event = null) => {
  if (event === null) event = await strapi.entityService.findOne(
      "api::event.event", data.params.where.id)
  // if it's being published and the event it doesn't already have a discussionId
  if ("publishedAt" in data.params.data)
    return data.params.data.publishedAt !== null && event.discussionId === null
  // if it's removing the discussionId and it's published (recreate the category?)
  if ("discussionId" in data.params.data)
    return data.params.data.discussionId === null && event.publishedAt !== null
  // if the event doesn't already have a discussionId and it's not being assigned one and it's published
  return event.discussionId === null &&
    !("discussionId" in data.params.data) && event.publishedAt !== null
}

module.exports = {
  canCreateEventDiscussion,
}
