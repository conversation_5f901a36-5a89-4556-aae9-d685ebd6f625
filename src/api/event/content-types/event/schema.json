{"kind": "collectionType", "collectionName": "events", "info": {"singularName": "event", "pluralName": "events", "displayName": "Practice | Audio Journeys & Clubs", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "cardTitle": {"type": "string", "required": false}, "headline": {"type": "string"}, "description": {"type": "text", "required": true}, "dateHeadline": {"type": "string", "required": false}, "thumbnail": {"allowedTypes": ["images"], "type": "media", "multiple": false, "required": false}, "picture": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "about": {"type": "richtext"}, "price": {"type": "decimal", "required": true, "default": 0}, "startDateTime": {"type": "datetime", "required": true}, "endDateTime": {"type": "datetime"}, "Curriculum": {"type": "component", "repeatable": false, "component": "sessions.curriculum", "required": true}, "modules": {"type": "component", "repeatable": true, "component": "sessions.event-module"}, "Facilitators": {"type": "component", "repeatable": false, "component": "sessions.facilitator-list"}, "InfoBox": {"type": "component", "repeatable": false, "component": "sessions.information-box", "required": true}, "categories": {"type": "relation", "relation": "oneToMany", "target": "api::category.category"}, "slug": {"type": "uid", "targetField": "title", "required": true}, "StudentTestimonial": {"displayName": "Student Testimonial", "type": "component", "repeatable": true, "component": "landing-pages.student-testimonial"}, "chargebee_entity_id": {"type": "string", "required": false}, "previewVideo": {"type": "relation", "relation": "oneToOne", "target": "plugin::mux-video-uploader.mux-asset"}, "previewDescription": {"type": "text"}, "previewThumbnail": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "previewTimecode": {"type": "integer", "min": 0, "required": false}, "Related": {"type": "dynamiczone", "components": ["media.related-course", "media.related-event", "media.related-article", "media.related-film", "media.related-podcast", "media.related-webinar"], "required": false, "min": 1}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo", "required": false}, "recurring": {"type": "boolean", "default": false, "required": true}, "hubspot_list_id": {"type": "string"}, "discussionId": {"type": "string"}}}