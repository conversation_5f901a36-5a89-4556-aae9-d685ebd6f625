const slugify = require("slugify");
const { v4: uuid } = require('uuid');

const {
  getRelatedContent,
  isContentAlreadyRelated,
  updateRelatedContent } = require('../../../../utils/content');

module.exports = {
  async beforeCreate(event) {
    if (!event.params.data.cardTitle && event.params.data.title) {
      event.params.data.cardTitle = event.params.data.title;
    }

    const entity_id = uuid();
    event.params.data.chargebee_entity_id = `films_${entity_id}`;
    if (!event.params.data.slug) {
      event.params.data.slug = slugify(
          event.params.data.title, { lower: true });
    }
  },
  async afterCreate(event) {
    // const data = {
    //   id: event.result.id,
    //   itemId: `films_${event.result.id}-${event.result.slug}`,
    //   itemName: `${event.result.title} [film]`,
    //   entityId: event.result.chargebee_entity_id,
    //   price: parseInt(event.result.price * 100),
    // }
    // await strapi.service(
    //     'api::subscription-plan.subscription-plan').createChargebeeChargeItem(data);
  },
  async beforeUpdate(event) {
    const watchEntry = await strapi.entityService.findOne(
        'api::watch-entry.watch-entry', event.params.where.id)


    if (!event.params.data.cardTitle && (event.params.data.title || watchEntry.title)) {
      event.params.data.cardTitle = event.params.data.title || watchEntry.title;
    }

    // if (event.params.data.price &&
    //     watchEntry.price !== event.params.data.price) {
    //   // update Chargebee item price
    //   await strapi.service('api::subscription-plan.subscription-plan')
    //     .updateChargebeeChargeItem({
    //       entityId: listenEntry.chargebee_entity_id,
    //       price: parseInt(event.params.data.price * 100)
    //     });
    // }
    if (event.params.data.slug &&
        watchEntry.slug !== event.params.data.slug) {
      // update API users' content with new slug
      await strapi.plugin('deployment-trigger').service('deployment')
        .updateSlug('films', watchEntry.slug, event.params.data.slug);
    }
  },
  async afterUpdate(event) {
    // TODO: check if teacher was removed and update it as well
    if (event.result?.TeacherList?.teachers) {
      // update entities related content
      for (let t of event.result.TeacherList.teachers) {
        const teacher = await strapi.entityService.findOne('api::entity.entity', t.id, {
          populate: { relatedFilms: true }
        })
        await strapi.entityService.update('api::entity.entity', t.id, {
          data: {
            relatedFilms: [...teacher.relatedFilms, event.params.where.id]
          }
        });
      }
    }

    if (event.result?.Related) {
      // add bidirectional relationship in the results in the Related field
      for (let related of event.result.Related) {
        const relatedContentType = `${related.__component.replace('media.related-', '')}s`
        const relatedContent = await getRelatedContent(strapi, related, relatedContentType)

        // if the event.result.id already exists in relatedFilm.Related, skip
        const alreadyRelated = isContentAlreadyRelated(
          relatedContent, 'film', event.result.id)

        // update every related film Related field with the current film (event.result)
        if (relatedContent.id && (!relatedContent.Related || !alreadyRelated) ) {
          await updateRelatedContent(
            strapi, relatedContent, relatedContentType, event.result.id, 'film')
        }
      }
    }
    // TODO: check if the related content was removed and sync it
    // Skip updatePages() and cache invalidation during bulk updates to prevent cascading
    const collectionsService = strapi.plugin('update-collections-trigger').service('collections');
    if (!collectionsService.isBulkUpdateInProgress()) {
      await strapi.plugin('deployment-trigger').service('deployment').updatePages();
      collectionsService.invalidateCache('films', event.result.slug);
    }
  },
};
