'use strict';
const axios = require('axios');


const {
  getRelatedContent,
  isContentAlreadyRelated,
  updateAllRelatedContent,
  updateSessionsWithContentId,
  updateTeacherRelatedContent,
} = require('../../../../utils/content');

const populateTeachersQuery = {
  teachersList: {
    populate: {
      teachers: {
        populate: {
          relatedCourses: true,
          relatedEvents: true,
          relatedArticles: true,
          relatedFilms: true,
          relatedPodcasts: true,
          relatedWebinars: true,
        }
      }
    }
  }
}

const populateRelatedContentQuery = {
  Related: {
    populate: [
      'course',
      'event',
      'article',
      'film',
      'podcast',
      'webinar',
    ]
  }
}

const mapRelatedContent = () => ({
  async mapCourseSessions(strapi) {
    try {
      const query = {
        populate: {
          ...populateTeachersQuery,
          course: true,
        }
      }
      // list all course sessions by id
      const courseSessions = await strapi.entityService.findMany(
        'api::course-session.course-session',
        query)

      console.log(`Processing ${courseSessions.length} course sessions...`)

      // Process in batches to manage memory
      const BATCH_SIZE = 50;
      for (let i = 0; i < courseSessions.length; i += BATCH_SIZE) {
        try {
          const batch = courseSessions.slice(i, i + BATCH_SIZE);
          console.log(`Processing course sessions batch ${Math.floor(i/BATCH_SIZE) + 1}/${Math.ceil(courseSessions.length/BATCH_SIZE)}`);

          for (const courseSession of batch) {
            try {
              // update teachers' related content
              const hasFullParams = courseSession.course && courseSession.teachersList && courseSession.teachersList.teachers
              if (hasFullParams) {
                for (let t of courseSession.teachersList.teachers) {
                  try {
                    const teacher = await strapi.entityService.findOne('api::entity.entity', t.id, {
                      populate: { relatedCourses: true }
                    })
                    if (teacher && teacher.relatedCourses && teacher.relatedCourses.some(c => c.id === courseSession.course.id)) {
                      // console.log(`related-content-mapping mapCourseSessions(): teacher ${t.name} [${t.id}] already has 'relatedCourses' ${courseSession.course.title} [${courseSession.course.id}]`)
                      continue
                    }
                    if (teacher && teacher.relatedCourses) {
                      await strapi.entityService.update('api::entity.entity', t.id, { data: {
                          relatedCourses: [...teacher.relatedCourses, courseSession.course]
                        } });
                    }
                  } catch (e) {
                    console.error(`related-content-mapping mapCourseSessions(): teacher ${t.name} [${t.id}] update failed for 'relatedCourses' ${courseSession.course?.title} [${courseSession.course?.id}]:`, e.message)
                  }
                }
              }
            } catch (e) {
              console.error(`Error processing course session ${courseSession.id}:`, e.message)
            }
          }

          // Force garbage collection between batches if available
          if (global.gc) {
            global.gc();
          }
        } catch (e) {
          console.error(`Error processing course sessions batch ${Math.floor(i/BATCH_SIZE) + 1}:`, e.message)
        }
      }
    } catch (e) {
      console.error(`Error in mapCourseSessions:`, e.message)
      throw e; // Re-throw to be caught by main updateCollections
    }
  },
  async mapEventSessions(strapi) {
    try {
      const query = {
        populate: {
          ...populateTeachersQuery,
          event: true,
        }
      }
      // list all event sessions
      const eventSessions = await strapi.entityService.findMany(
        'api::event-session.event-session',
        query)

      console.log(`Processing ${eventSessions.length} event sessions...`)

      // Process in batches to manage memory
      const BATCH_SIZE = 50;
      for (let i = 0; i < eventSessions.length; i += BATCH_SIZE) {
        try {
          const batch = eventSessions.slice(i, i + BATCH_SIZE);
          console.log(`Processing event sessions batch ${Math.floor(i/BATCH_SIZE) + 1}/${Math.ceil(eventSessions.length/BATCH_SIZE)}`);

          for (const eventSession of batch) {
            try {
              // update teachers' related content
              const hasFullParams = eventSession.event && eventSession.teachersList && eventSession.teachersList.teachers
              if (hasFullParams) {
                for (let t of eventSession.teachersList.teachers) {
                  try {
                    const teacher = await strapi.entityService.findOne('api::entity.entity', t.id, {
                      populate: { relatedEvents: true }
                    })
                    if (teacher && teacher.relatedEvents && teacher.relatedEvents.some(c => c.id === eventSession.event.id)) {
                      // console.log(`related-content-mapping mapEventSessions(): teacher ${t.name} [${t.id}] already has 'relatedEvents' ${eventSession.event.title} [${eventSession.event.id}]`)
                      continue
                    }
                    if (teacher && teacher.relatedEvents) {
                      await strapi.entityService.update('api::entity.entity', t.id, { data: {
                          relatedEvents: [...teacher.relatedEvents, eventSession.event]
                        } });
                    }
                  } catch (e) {
                    console.error(`related-content-mapping mapEventSessions(): teacher ${t.name} [${t.id}] update failed for 'relatedEvents' ${eventSession.event?.title} [${eventSession.event?.id}]:`, e.message)
                  }
                }
              }
            } catch (e) {
              console.error(`Error processing event session ${eventSession.id}:`, e.message)
            }
          }

          // Force garbage collection between batches if available
          if (global.gc) {
            global.gc();
          }
        } catch (e) {
          console.error(`Error processing event sessions batch ${Math.floor(i/BATCH_SIZE) + 1}:`, e.message)
        }
      }
    } catch (e) {
      console.error(`Error in mapEventSessions:`, e.message)
      throw e; // Re-throw to be caught by main updateCollections
    }
  },
  async mapCourses(strapi) {
    try {
      const populateQuery = {
        populate: {
          modules: { populate: { sessions: true } },
          ...populateRelatedContentQuery,
        }
      }

      // list all courses
      const courses = await strapi.entityService.findMany(
        'api::course.course',
        populateQuery)

      console.log(`Processing ${courses.length} courses...`)

      // Process in batches to manage memory
      const BATCH_SIZE = 25; // Smaller batch size for courses due to complex relationships
      for (let i = 0; i < courses.length; i += BATCH_SIZE) {
        try {
          const batch = courses.slice(i, i + BATCH_SIZE);
          console.log(`Processing courses batch ${Math.floor(i/BATCH_SIZE) + 1}/${Math.ceil(courses.length/BATCH_SIZE)}`);

          for(const course of batch){
            try {
              if (course.Related) {
                // add bidirectional relationship in the results in the Related field
                await updateAllRelatedContent(strapi, course, 'course')
              }
              await updateSessionsWithContentId(course, 'course')

              await populateCardTitle(strapi, 'api::course.course', course);
            } catch (e) {
              console.error(`Error processing course ${course.id} (${course.title}):`, e.message)
            }
          }

          // Force garbage collection between batches if available
          if (global.gc) {
            global.gc();
          }
        } catch (e) {
          console.error(`Error processing courses batch ${Math.floor(i/BATCH_SIZE) + 1}:`, e.message)
        }
      }
    } catch (e) {
      console.error(`Error in mapCourses:`, e.message)
      throw e; // Re-throw to be caught by main updateCollections
    }
  },
  async mapEvents(strapi) {
    try {
      const query = {
        populate: {
          ...populateRelatedContentQuery,
        }
      }

      // list all events
      const events = await strapi.entityService.findMany(
        'api::event.event',
        query)

      console.log(`Processing ${events.length} events...`)

      for(const event of events){
        try {
          if (event.Related) {
            // add bidirectional relationship in the results in the Related field
            await updateAllRelatedContent(strapi, event, 'event')
          }

          await populateCardTitle(strapi, 'api::event.event', event);
        } catch (e) {
          console.error(`Error processing event ${event.id} (${event.title}):`, e.message)
        }
      }
    } catch (e) {
      console.error(`Error in mapEvents:`, e.message)
      throw e; // Re-throw to be caught by main updateCollections
    }
  },
  async mapPodcasts(strapi) {
    try {
      const query = {
        populate: {
          TeacherList: populateTeachersQuery.teachersList,
          ...populateRelatedContentQuery,
        }
      }
      // list all podcasts
      const podcasts = await strapi.entityService.findMany(
        'api::listen-entry.listen-entry',
        query)

      console.log(`Processing ${podcasts.length} podcasts...`)

      // TODO: check if teacher was removed and update it as well
      for(const podcast of podcasts){
        try {
          // update teachers' related content
          if (podcast.TeacherList && podcast.TeacherList.teachers) {
            await updateTeacherRelatedContent(strapi, podcast, 'relatedPodcasts')
          }

          if (podcast.Related) {
            // add bidirectional relationship in the results in the Related field
            await updateAllRelatedContent(strapi, podcast, 'podcast')
          }

          await populateCardTitle(strapi, 'api::listen-entry.listen-entry', podcast);
        } catch (e) {
          console.error(`Error processing podcast ${podcast.id} (${podcast.title}):`, e.message)
        }
      }
    } catch (e) {
      console.error(`Error in mapPodcasts:`, e.message)
      throw e; // Re-throw to be caught by main updateCollections
    }
  },
  async mapArticles(strapi) {
    try {
      const query = {
        populate: {
          TeacherList: populateTeachersQuery.teachersList,
          ...populateRelatedContentQuery,
        }
      }
      // list all articles
      const articles = await strapi.entityService.findMany(
        'api::read-entry.read-entry',
        query)

      console.log(`Processing ${articles.length} articles...`)

      // TODO: check if teacher was removed and update it as well
      for (const article of articles) {
        try {
          // update teachers' related content
          if (article.TeacherList && article.TeacherList.teachers) {
            await updateTeacherRelatedContent(strapi, article, 'relatedArticles')
          }

          if (article.Related) {
            // add bidirectional relationship in the results in the Related field
            await updateAllRelatedContent(strapi, article, 'article')
          }

          await populateCardTitle(strapi, 'api::read-entry.read-entry', article);
        } catch (e) {
          console.error(`Error processing article ${article.id} (${article.title}):`, e.message)
        }
      }
    } catch (e) {
      console.error(`Error in mapArticles:`, e.message)
      throw e; // Re-throw to be caught by main updateCollections
    }
  },
  async mapFilms(strapi) {
    try {
      const query = {
        populate: {
          TeacherList: populateTeachersQuery.teachersList,
          ...populateRelatedContentQuery,
        }
      }

      // list all films
      const films = await strapi.entityService.findMany(
        'api::watch-entry.watch-entry',
        query)

      console.log(`Processing ${films.length} films...`)

      // TODO: check if teacher was removed and update it as well
      for(const film of films){
        try {
          // update teachers' related content
          if (film.TeacherList && film.TeacherList.teachers) {
            await updateTeacherRelatedContent(strapi, film, 'relatedFilms')
          }

          if (film.Related) {
            // add bidirectional relationship in the results in the Related field
            await updateAllRelatedContent(strapi, film, 'film')
          }

          await populateCardTitle(strapi, 'api::watch-entry.watch-entry', film);
        } catch (e) {
          console.error(`Error processing film ${film.id} (${film.title}):`, e.message)
        }
      }
    } catch (e) {
      console.error(`Error in mapFilms:`, e.message)
      throw e; // Re-throw to be caught by main updateCollections
    }
  },
  async mapWebinars(strapi) {
    try {
      const query = {
        populate: {
          TeacherList: populateTeachersQuery.teachersList,
          ...populateRelatedContentQuery,
        }
      }

      // list all webinars
      const webinars = await strapi.entityService.findMany(
        'api::webinar.webinar',
        query)

      console.log(`Processing ${webinars.length} webinars...`)

      // TODO: check if teacher was removed and update it as well
      for(const webinar of webinars){
        try {
          // update teachers' related content
          if (webinar.TeacherList && webinar.TeacherList.teachers) {
            await updateTeacherRelatedContent(strapi, webinar, 'relatedWebinars')
          }

          if (webinar.Related) {
            // add bidirectional relationship in the results in the Related field
            await updateAllRelatedContent(strapi, webinar, 'webinar')
          }

          await populateCardTitle(strapi, 'api::webinar.webinar', webinar);
        } catch (e) {
          console.error(`Error processing webinar ${webinar.id} (${webinar.title}):`, e.message)
        }
      }
    } catch (e) {
      console.error(`Error in mapWebinars:`, e.message)
      throw e; // Re-throw to be caught by main updateCollections
    }
  },
  async mapTeachersUserAccounts(strapi) {
    try {
      // list all teachers who don't have a user account
      const query = {
        filters: {
          $or: [
            {
              user_id: "",
            },
            {
              user_id: null,
            },
            {
              user_id: undefined,
            },
            {
              user_email: "",
            },
            {
              user_email: null,
            },
            {
              user_email: undefined,
            },
          ]
        }
      }

      const teachers = await strapi.entityService.findMany('api::entity.entity', query)

      console.log(`update-collections-trigger: updating ${teachers.length} teacher user accounts...`)

      // Process in smaller batches with delays to prevent overwhelming the system
      const BATCH_SIZE = 10;
      for (let i = 0; i < teachers.length; i += BATCH_SIZE) {
        try {
          const batch = teachers.slice(i, i + BATCH_SIZE);
          console.log(`Processing teacher accounts batch ${Math.floor(i/BATCH_SIZE) + 1}/${Math.ceil(teachers.length/BATCH_SIZE)}`);

          for(const teacher of batch){
            try {
              // update entity so that the beforeUpdate lifecycle trigger creates the user account
              await strapi.entityService.update('api::entity.entity', teacher.id, { data: { id: teacher.id, } })
              await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (e) {
              console.error(`Error updating teacher account ${teacher.id} (${teacher.name}):`, e.message)
            }
          }

          // Longer delay between batches for user account creation
          if (i + BATCH_SIZE < teachers.length) {
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        } catch (e) {
          console.error(`Error processing teacher accounts batch ${Math.floor(i/BATCH_SIZE) + 1}:`, e.message)
        }
      }
    } catch (e) {
      console.error(`Error in mapTeachersUserAccounts:`, e.message)
      throw e; // Re-throw to be caught by main updateCollections
    }
  },
  async mapCardTitles(strapi) {
    try {
      const contentTypeMapping = {
        'api::entity.entity': { sourceField: 'name', displayName: 'teachers' },
        'api::read-entry.read-entry': { sourceField: 'title', displayName: 'articles' },
        'api::listen-entry.listen-entry': { sourceField: 'title', displayName: 'podcasts' },
        'api::watch-entry.watch-entry': { sourceField: 'title', displayName: 'films' },
        'api::webinar.webinar': { sourceField: 'title', displayName: 'webinars' },
        'api::event.event': { sourceField: 'title', displayName: 'events' },
        'api::event-session.event-session': { sourceField: 'title', displayName: 'event-sessions' },
        'api::course.course': { sourceField: 'title', displayName: 'courses' },
        'api::course-session.course-session': { sourceField: 'title', displayName: 'course-sessions' },
      };

      for (const [contentType, config] of Object.entries(contentTypeMapping)) {
        try {
          console.log(`Processing cardTitle mapping for ${config.displayName}...`);

          // Find entities with empty cardTitle
          const entities = await strapi.entityService.findMany(contentType, {
            filters: {
              $or: [
                { cardTitle: { $null: true } },
                { cardTitle: '' }
              ]
            }
          });

          console.log(`Found ${entities.length} ${config.displayName} with empty cardTitle`);

          // Process in batches
          const BATCH_SIZE = 50;
          for (let i = 0; i < entities.length; i += BATCH_SIZE) {
            const batch = entities.slice(i, i + BATCH_SIZE);
            console.log(`Processing ${config.displayName} cardTitle batch ${Math.floor(i/BATCH_SIZE) + 1}/${Math.ceil(entities.length/BATCH_SIZE)}`);

            for (const entity of batch) {
              try {
                const sourceValue = entity[config.sourceField];
                if (sourceValue) {
                  await strapi.entityService.update(contentType, entity.id, {
                    data: { cardTitle: sourceValue }
                  });
                  console.log(`Updated cardTitle for ${config.displayName} ${entity.id}: "${sourceValue}"`);
                }
              } catch (e) {
                console.error(`Error updating cardTitle for ${config.displayName} ${entity.id}:`, e.message);
              }
            }

            // Small delay between batches
            if (i + BATCH_SIZE < entities.length) {
              await new Promise(resolve => setTimeout(resolve, 100));
            }
          }
        } catch (e) {
          console.error(`Error processing cardTitle for ${config.displayName}:`, e.message);
        }
      }
    } catch (e) {
      console.error(`Error in mapCardTitles:`, e.message);
      throw e; // Re-throw to be caught by main updateCollections
    }
  }
})

// Helper function to ensure cardTitle is populated
const populateCardTitle = async (strapi, contentType, entity) => {
  try {
    let needsUpdate = false;
    let cardTitle = entity.cardTitle;

    // Determine the source field based on content type
    let sourceField;
    if (contentType === 'api::entity.entity') {
      sourceField = entity.name;
    } else {
      sourceField = entity.title;
    }

    // If cardTitle is empty but source field exists, populate it
    if (!cardTitle && sourceField) {
      cardTitle = sourceField;
      needsUpdate = true;
    }

    // Update the entity if needed
    if (needsUpdate) {
      await strapi.entityService.update(contentType, entity.id, {
        data: { cardTitle }
      });
      console.log(`Updated cardTitle for ${contentType} ${entity.id}: "${cardTitle}"`);
    }
  } catch (error) {
    console.error(`Error updating cardTitle for ${contentType} ${entity.id}:`, error.message);
  }
};

const cacheManagement = () => ({
  invalidateCache(contentType, contentSlug = false, sessionSlug = false) {
    try {
      const apiBase = process.env.API_URL
      const magicKey = process.env.API_MAGIC_KEY

      if (!apiBase || !magicKey) {
        console.warn(`invalidate-cache: Missing API_URL or API_MAGIC_KEY environment variables`)
        return
      }

      const cacheData = { contentType }
      if (contentSlug) cacheData.slug = contentSlug
      if (sessionSlug) cacheData.session = sessionSlug

      axios.post(
        `${apiBase}/dispatcher/invalidate_cache?magicKey=${magicKey}`,
        { cacheData })
        .then(() => {
          console.log(`invalidate-cache: ${contentType}/${contentSlug}/${sessionSlug || ''}`)
        })
        .catch((error) => {
          console.error(`invalidate-cache failed for ${contentType}/${contentSlug}/${sessionSlug || ''}:`, error.message)
        })
    } catch (e) {
      console.error(`invalidate-cache error for ${contentType}/${contentSlug}/${sessionSlug || ''}:`, e.message)
    }
  }
})

// Global flag to prevent cascading updates during bulk operations
let isBulkUpdateInProgress = false;

module.exports = ({ strapi }) => ({
  async updateCollections(){
    try {
      // Set flag to prevent cascading lifecycle hooks
      isBulkUpdateInProgress = true;
      console.log("update-collections-trigger: Starting bulk update process...")

      const startTime = Date.now();
      let errors = [];

      // Updates entities' user accounts
      if (Boolean(Number(process.env.TEACHERS_USER_ACCOUNTS))) {
        try {
          await mapRelatedContent().mapTeachersUserAccounts(strapi)
          console.log("update-collections-trigger: teachers' user accounts created successfully")
        } catch (e) {
          console.error("update-collections-trigger: Error in mapTeachersUserAccounts:", e.message)
          errors.push(`mapTeachersUserAccounts: ${e.message}`)
        }
      }

      // Updates all teacher's related content
      try {
        await mapRelatedContent().mapCourseSessions(strapi)
        console.log("update-collections-trigger: courses' teachers' content updated successfully")
      } catch (e) {
        console.error("update-collections-trigger: Error in mapCourseSessions:", e.message)
        errors.push(`mapCourseSessions: ${e.message}`)
      }

      try {
        await mapRelatedContent().mapEventSessions(strapi)
        console.log("update-collections-trigger: events' teachers' content updated successfully")
      } catch (e) {
        console.error("update-collections-trigger: Error in mapEventSessions:", e.message)
        errors.push(`mapEventSessions: ${e.message}`)
      }

      try {
        await mapRelatedContent().mapCourses(strapi)
        console.log("update-collections-trigger: related courses updated successfully")
      } catch (e) {
        console.error("update-collections-trigger: Error in mapCourses:", e.message)
        errors.push(`mapCourses: ${e.message}`)
      }

      try {
        await mapRelatedContent().mapEvents(strapi)
        console.log("update-collections-trigger: related events updated successfully")
      } catch (e) {
        console.error("update-collections-trigger: Error in mapEvents:", e.message)
        errors.push(`mapEvents: ${e.message}`)
      }

      try {
        await mapRelatedContent().mapPodcasts(strapi)
        console.log("update-collections-trigger: related podcasts updated successfully")
      } catch (e) {
        console.error("update-collections-trigger: Error in mapPodcasts:", e.message)
        errors.push(`mapPodcasts: ${e.message}`)
      }

      try {
        await mapRelatedContent().mapArticles(strapi)
        console.log("update-collections-trigger: related articles updated successfully")
      } catch (e) {
        console.error("update-collections-trigger: Error in mapArticles:", e.message)
        errors.push(`mapArticles: ${e.message}`)
      }

      try {
        await mapRelatedContent().mapFilms(strapi)
        console.log("update-collections-trigger: related films updated successfully")
      } catch (e) {
        console.error("update-collections-trigger: Error in mapFilms:", e.message)
        errors.push(`mapFilms: ${e.message}`)
      }

      try {
        await mapRelatedContent().mapWebinars(strapi)
        console.log("update-collections-trigger: related webinars updated successfully")
      } catch (e) {
        console.error("update-collections-trigger: Error in mapWebinars:", e.message)
        errors.push(`mapWebinars: ${e.message}`)
      }

      // Map cardTitles for all content types
      try {
        await mapRelatedContent().mapCardTitles(strapi)
        console.log("update-collections-trigger: cardTitle mapping completed successfully")
      } catch (e) {
        console.error("update-collections-trigger: Error in mapCardTitles:", e.message)
        errors.push(`mapCardTitles: ${e.message}`)
      }

      // Clear flag before final operations
      isBulkUpdateInProgress = false;

      // Now run updatePages() once at the end
      try {
        console.log("update-collections-trigger: Updating pages...")
        await strapi.plugin('deployment-trigger').service('deployment').updatePages();
        console.log("update-collections-trigger: Pages updated successfully")
      } catch (e) {
        console.error("update-collections-trigger: Error updating pages:", e.message)
        errors.push(`updatePages: ${e.message}`)
      }

      // Batch invalidate cache for major content types
      try {
        console.log("update-collections-trigger: Invalidating caches...")
        this.invalidateCache('courses');
        this.invalidateCache('events');
        this.invalidateCache('articles');
        this.invalidateCache('films');
        this.invalidateCache('podcasts');
        this.invalidateCache('webinars');
        this.invalidateCache('entities');
        this.invalidateCache('pages');
        console.log("update-collections-trigger: Cache invalidation completed")
      } catch (e) {
        console.error("update-collections-trigger: Error invalidating caches:", e.message)
        errors.push(`cache invalidation: ${e.message}`)
      }

      const endTime = Date.now();
      const duration = Math.round((endTime - startTime) / 1000);

      if (errors.length > 0) {
        console.warn(`update-collections-trigger: Bulk update completed with ${errors.length} errors in ${duration}s:`, errors)
      } else {
        console.log(`update-collections-trigger: Bulk update process completed successfully in ${duration}s`)
      }

      return {
        success: true,
        duration,
        errors: errors.length > 0 ? errors : null
      }
    } catch (e) {
      console.error("update-collections-trigger: Critical error during bulk update:", e.message)
      // Ensure flag is cleared even on error
      isBulkUpdateInProgress = false;
      throw e;
    }
  },
  invalidateCache(contentType, contentSlug = null, sessionSlug = null) {
    try {
      cacheManagement().invalidateCache(contentType, contentSlug, sessionSlug)
    } catch (e) {
      console.error(`Error in invalidateCache for ${contentType}:`, e.message)
    }
  },
  isBulkUpdateInProgress() {
    return isBulkUpdateInProgress;
  }
});
